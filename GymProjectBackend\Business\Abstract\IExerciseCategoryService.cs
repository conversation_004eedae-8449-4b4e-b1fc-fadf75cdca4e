using Core.Utilities.Results;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Business.Abstract
{
    public interface IExerciseCategoryService
    {
        IDataResult<List<ExerciseCategoryDto>> GetAllCategories();
        IDataResult<List<ExerciseCategoryDto>> GetActiveCategories();
        IDataResult<ExerciseCategoryDto> GetById(int categoryId);
        IResult Add(ExerciseCategoryAddDto categoryAddDto);
        IResult Update(ExerciseCategoryUpdateDto categoryUpdateDto);
        IResult Delete(int categoryId);
    }
}
