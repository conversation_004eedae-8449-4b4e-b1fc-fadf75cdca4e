{"version": 3, "sources": ["../../../../../../node_modules/@angular/material/fesm2022/ripple-loader-64444b06.mjs"], "sourcesContent": ["import { DOCUMENT } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { inject, ANIMATION_MODULE_TYPE, NgZone, Injector, RendererFactory2, Injectable } from '@angular/core';\nimport { Platform, _bindEventWithOptions, _getEventTarget } from '@angular/cdk/platform';\nimport { a as MAT_RIPPLE_GLOBAL_OPTIONS, R as RippleRenderer, d as defaultRippleAnimationConfig } from './ripple-33861831.mjs';\n\n/** The options for the MatRippleLoader's event listeners. */\nconst eventListenerOptions = {\n  capture: true\n};\n/**\n * The events that should trigger the initialization of the ripple.\n * Note that we use `mousedown`, rather than `click`, for mouse devices because\n * we can't rely on `mouseenter` in the shadow DOM and `click` happens too late.\n */\nconst rippleInteractionEvents = ['focus', 'mousedown', 'mouseenter', 'touchstart'];\n/** The attribute attached to a component whose ripple has not yet been initialized. */\nconst matRippleUninitialized = 'mat-ripple-loader-uninitialized';\n/** Additional classes that should be added to the ripple when it is rendered. */\nconst matRippleClassName = 'mat-ripple-loader-class-name';\n/** Whether the ripple should be centered. */\nconst matRippleCentered = 'mat-ripple-loader-centered';\n/** Whether the ripple should be disabled. */\nconst matRippleDisabled = 'mat-ripple-loader-disabled';\n/**\n * Handles attaching ripples on demand.\n *\n * This service allows us to avoid eagerly creating & attaching MatRipples.\n * It works by creating & attaching a ripple only when a component is first interacted with.\n *\n * @docs-private\n */\nclass MatRippleLoader {\n  _document = inject(DOCUMENT);\n  _animationMode = inject(ANIMATION_MODULE_TYPE, {\n    optional: true\n  });\n  _globalRippleOptions = inject(MAT_RIPPLE_GLOBAL_OPTIONS, {\n    optional: true\n  });\n  _platform = inject(Platform);\n  _ngZone = inject(NgZone);\n  _injector = inject(Injector);\n  _eventCleanups;\n  _hosts = new Map();\n  constructor() {\n    const renderer = inject(RendererFactory2).createRenderer(null, null);\n    this._eventCleanups = this._ngZone.runOutsideAngular(() => {\n      return rippleInteractionEvents.map(name => _bindEventWithOptions(renderer, this._document, name, this._onInteraction, eventListenerOptions));\n    });\n  }\n  ngOnDestroy() {\n    const hosts = this._hosts.keys();\n    for (const host of hosts) {\n      this.destroyRipple(host);\n    }\n    this._eventCleanups.forEach(cleanup => cleanup());\n  }\n  /**\n   * Configures the ripple that will be rendered by the ripple loader.\n   *\n   * Stores the given information about how the ripple should be configured on the host\n   * element so that it can later be retrived & used when the ripple is actually created.\n   */\n  configureRipple(host, config) {\n    // Indicates that the ripple has not yet been rendered for this component.\n    host.setAttribute(matRippleUninitialized, this._globalRippleOptions?.namespace ?? '');\n    // Store the additional class name(s) that should be added to the ripple element.\n    if (config.className || !host.hasAttribute(matRippleClassName)) {\n      host.setAttribute(matRippleClassName, config.className || '');\n    }\n    // Store whether the ripple should be centered.\n    if (config.centered) {\n      host.setAttribute(matRippleCentered, '');\n    }\n    if (config.disabled) {\n      host.setAttribute(matRippleDisabled, '');\n    }\n  }\n  /** Sets the disabled state on the ripple instance corresponding to the given host element. */\n  setDisabled(host, disabled) {\n    const ripple = this._hosts.get(host);\n    // If the ripple has already been instantiated, just disable it.\n    if (ripple) {\n      ripple.target.rippleDisabled = disabled;\n      if (!disabled && !ripple.hasSetUpEvents) {\n        ripple.hasSetUpEvents = true;\n        ripple.renderer.setupTriggerEvents(host);\n      }\n    } else if (disabled) {\n      // Otherwise, set an attribute so we know what the\n      // disabled state should be when the ripple is initialized.\n      host.setAttribute(matRippleDisabled, '');\n    } else {\n      host.removeAttribute(matRippleDisabled);\n    }\n  }\n  /**\n   * Handles creating and attaching component internals\n   * when a component is initially interacted with.\n   */\n  _onInteraction = event => {\n    const eventTarget = _getEventTarget(event);\n    if (eventTarget instanceof HTMLElement) {\n      // TODO(wagnermaciel): Consider batching these events to improve runtime performance.\n      const element = eventTarget.closest(`[${matRippleUninitialized}=\"${this._globalRippleOptions?.namespace ?? ''}\"]`);\n      if (element) {\n        this._createRipple(element);\n      }\n    }\n  };\n  /** Creates a MatRipple and appends it to the given element. */\n  _createRipple(host) {\n    if (!this._document || this._hosts.has(host)) {\n      return;\n    }\n    // Create the ripple element.\n    host.querySelector('.mat-ripple')?.remove();\n    const rippleEl = this._document.createElement('span');\n    rippleEl.classList.add('mat-ripple', host.getAttribute(matRippleClassName));\n    host.append(rippleEl);\n    const isNoopAnimations = this._animationMode === 'NoopAnimations';\n    const globalOptions = this._globalRippleOptions;\n    const enterDuration = isNoopAnimations ? 0 : globalOptions?.animation?.enterDuration ?? defaultRippleAnimationConfig.enterDuration;\n    const exitDuration = isNoopAnimations ? 0 : globalOptions?.animation?.exitDuration ?? defaultRippleAnimationConfig.exitDuration;\n    const target = {\n      rippleDisabled: isNoopAnimations || globalOptions?.disabled || host.hasAttribute(matRippleDisabled),\n      rippleConfig: {\n        centered: host.hasAttribute(matRippleCentered),\n        terminateOnPointerUp: globalOptions?.terminateOnPointerUp,\n        animation: {\n          enterDuration,\n          exitDuration\n        }\n      }\n    };\n    const renderer = new RippleRenderer(target, this._ngZone, rippleEl, this._platform, this._injector);\n    const hasSetUpEvents = !target.rippleDisabled;\n    if (hasSetUpEvents) {\n      renderer.setupTriggerEvents(host);\n    }\n    this._hosts.set(host, {\n      target,\n      renderer,\n      hasSetUpEvents\n    });\n    host.removeAttribute(matRippleUninitialized);\n  }\n  destroyRipple(host) {\n    const ripple = this._hosts.get(host);\n    if (ripple) {\n      ripple.renderer._removeTriggerEvents();\n      this._hosts.delete(host);\n    }\n  }\n  static ɵfac = function MatRippleLoader_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatRippleLoader)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: MatRippleLoader,\n    factory: MatRippleLoader.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatRippleLoader, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\nexport { MatRippleLoader as M };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAOA,IAAM,uBAAuB;AAAA,EAC3B,SAAS;AACX;AAMA,IAAM,0BAA0B,CAAC,SAAS,aAAa,cAAc,YAAY;AAEjF,IAAM,yBAAyB;AAE/B,IAAM,qBAAqB;AAE3B,IAAM,oBAAoB;AAE1B,IAAM,oBAAoB;AAS1B,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACpB,YAAY,OAAO,QAAQ;AAAA,EAC3B,iBAAiB,OAAO,uBAAuB;AAAA,IAC7C,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,uBAAuB,OAAO,2BAA2B;AAAA,IACvD,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,YAAY,OAAO,QAAQ;AAAA,EAC3B,UAAU,OAAO,MAAM;AAAA,EACvB,YAAY,OAAO,QAAQ;AAAA,EAC3B;AAAA,EACA,SAAS,oBAAI,IAAI;AAAA,EACjB,cAAc;AACZ,UAAM,WAAW,OAAO,gBAAgB,EAAE,eAAe,MAAM,IAAI;AACnE,SAAK,iBAAiB,KAAK,QAAQ,kBAAkB,MAAM;AACzD,aAAO,wBAAwB,IAAI,UAAQ,sBAAsB,UAAU,KAAK,WAAW,MAAM,KAAK,gBAAgB,oBAAoB,CAAC;AAAA,IAC7I,CAAC;AAAA,EACH;AAAA,EACA,cAAc;AACZ,UAAM,QAAQ,KAAK,OAAO,KAAK;AAC/B,eAAW,QAAQ,OAAO;AACxB,WAAK,cAAc,IAAI;AAAA,IACzB;AACA,SAAK,eAAe,QAAQ,aAAW,QAAQ,CAAC;AAAA,EAClD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,gBAAgB,MAAM,QAAQ;AAE5B,SAAK,aAAa,wBAAwB,KAAK,sBAAsB,aAAa,EAAE;AAEpF,QAAI,OAAO,aAAa,CAAC,KAAK,aAAa,kBAAkB,GAAG;AAC9D,WAAK,aAAa,oBAAoB,OAAO,aAAa,EAAE;AAAA,IAC9D;AAEA,QAAI,OAAO,UAAU;AACnB,WAAK,aAAa,mBAAmB,EAAE;AAAA,IACzC;AACA,QAAI,OAAO,UAAU;AACnB,WAAK,aAAa,mBAAmB,EAAE;AAAA,IACzC;AAAA,EACF;AAAA;AAAA,EAEA,YAAY,MAAM,UAAU;AAC1B,UAAM,SAAS,KAAK,OAAO,IAAI,IAAI;AAEnC,QAAI,QAAQ;AACV,aAAO,OAAO,iBAAiB;AAC/B,UAAI,CAAC,YAAY,CAAC,OAAO,gBAAgB;AACvC,eAAO,iBAAiB;AACxB,eAAO,SAAS,mBAAmB,IAAI;AAAA,MACzC;AAAA,IACF,WAAW,UAAU;AAGnB,WAAK,aAAa,mBAAmB,EAAE;AAAA,IACzC,OAAO;AACL,WAAK,gBAAgB,iBAAiB;AAAA,IACxC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,iBAAiB,WAAS;AACxB,UAAM,cAAc,gBAAgB,KAAK;AACzC,QAAI,uBAAuB,aAAa;AAEtC,YAAM,UAAU,YAAY,QAAQ,IAAI,sBAAsB,KAAK,KAAK,sBAAsB,aAAa,EAAE,IAAI;AACjH,UAAI,SAAS;AACX,aAAK,cAAc,OAAO;AAAA,MAC5B;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAEA,cAAc,MAAM;AAClB,QAAI,CAAC,KAAK,aAAa,KAAK,OAAO,IAAI,IAAI,GAAG;AAC5C;AAAA,IACF;AAEA,SAAK,cAAc,aAAa,GAAG,OAAO;AAC1C,UAAM,WAAW,KAAK,UAAU,cAAc,MAAM;AACpD,aAAS,UAAU,IAAI,cAAc,KAAK,aAAa,kBAAkB,CAAC;AAC1E,SAAK,OAAO,QAAQ;AACpB,UAAM,mBAAmB,KAAK,mBAAmB;AACjD,UAAM,gBAAgB,KAAK;AAC3B,UAAM,gBAAgB,mBAAmB,IAAI,eAAe,WAAW,iBAAiB,6BAA6B;AACrH,UAAM,eAAe,mBAAmB,IAAI,eAAe,WAAW,gBAAgB,6BAA6B;AACnH,UAAM,SAAS;AAAA,MACb,gBAAgB,oBAAoB,eAAe,YAAY,KAAK,aAAa,iBAAiB;AAAA,MAClG,cAAc;AAAA,QACZ,UAAU,KAAK,aAAa,iBAAiB;AAAA,QAC7C,sBAAsB,eAAe;AAAA,QACrC,WAAW;AAAA,UACT;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,UAAM,WAAW,IAAI,eAAe,QAAQ,KAAK,SAAS,UAAU,KAAK,WAAW,KAAK,SAAS;AAClG,UAAM,iBAAiB,CAAC,OAAO;AAC/B,QAAI,gBAAgB;AAClB,eAAS,mBAAmB,IAAI;AAAA,IAClC;AACA,SAAK,OAAO,IAAI,MAAM;AAAA,MACpB;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AACD,SAAK,gBAAgB,sBAAsB;AAAA,EAC7C;AAAA,EACA,cAAc,MAAM;AAClB,UAAM,SAAS,KAAK,OAAO,IAAI,IAAI;AACnC,QAAI,QAAQ;AACV,aAAO,SAAS,qBAAqB;AACrC,WAAK,OAAO,OAAO,IAAI;AAAA,IACzB;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,wBAAwB,mBAAmB;AAChE,WAAO,KAAK,qBAAqB,kBAAiB;AAAA,EACpD;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,iBAAgB;AAAA,IACzB,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;", "names": []}