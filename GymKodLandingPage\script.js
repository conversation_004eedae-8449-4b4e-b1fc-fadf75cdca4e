document.addEventListener('DOMContentLoaded', function() {
    // AOS Animasyon Kütüphanesini Başlat - Geliştirilmiş Ayarlar
    AOS.init({
        duration: 1000,
        easing: 'ease-out',
        once: false,
        mirror: false,
        offset: 120,
        delay: 100,
        anchorPlacement: 'top-bottom'
    });

    // AOS başlatıldıktan sonra, tüm FAQ öğelerini kapalı hale getir
    setTimeout(() => {
        const faqItems = document.querySelectorAll('.faq-item');
        faqItems.forEach(item => {
            item.classList.remove('active');
        });
    }, 100);

    // Pricing butonlarına ikon ekle
    const pricingButtons = document.querySelectorAll('.pricing-btn, .package-btn');
    pricingButtons.forEach(button => {
        if (!button.querySelector('i')) {
            button.innerHTML = button.textContent + ' <i class="fas fa-arrow-right"></i>';
        }
    });

    // Particles.js <PERSON> (Hero Bölüm<PERSON>)
    if (document.getElementById('particles-js')) {
        particlesJS('particles-js', {
            "particles": {
                "number": {
                    "value": 100,
                    "density": {
                        "enable": true,
                        "value_area": 1000
                    }
                },
                "color": {
                    "value": "#4361ee"
                },
                "shape": {
                    "type": "circle",
                    "stroke": {
                        "width": 0,
                        "color": "#000000"
                    },
                    "polygon": {
                        "nb_sides": 5
                    }
                },
                "opacity": {
                    "value": 0.5,
                    "random": false,
                    "anim": {
                        "enable": false,
                        "speed": 1,
                        "opacity_min": 0.1,
                        "sync": false
                    }
                },
                "size": {
                    "value": 3,
                    "random": true,
                    "anim": {
                        "enable": false,
                        "speed": 40,
                        "size_min": 0.1,
                        "sync": false
                    }
                },
                "line_linked": {
                    "enable": true,
                    "distance": 150,
                    "color": "#4361ee",
                    "opacity": 0.4,
                    "width": 1
                },
                "move": {
                    "enable": true,
                    "speed": 2,
                    "direction": "none",
                    "random": false,
                    "straight": false,
                    "out_mode": "out",
                    "bounce": false,
                    "attract": {
                        "enable": false,
                        "rotateX": 600,
                        "rotateY": 1200
                    }
                }
            },
            "interactivity": {
                "detect_on": "canvas",
                "events": {
                    "onhover": {
                        "enable": true,
                        "mode": "grab"
                    },
                    "onclick": {
                        "enable": true,
                        "mode": "push"
                    },
                    "resize": true
                },
                "modes": {
                    "grab": {
                        "distance": 140,
                        "line_linked": {
                            "opacity": 1
                        }
                    },
                    "bubble": {
                        "distance": 400,
                        "size": 40,
                        "duration": 2,
                        "opacity": 8,
                        "speed": 3
                    },
                    "repulse": {
                        "distance": 200,
                        "duration": 0.4
                    },
                    "push": {
                        "particles_nb": 4
                    },
                    "remove": {
                        "particles_nb": 2
                    }
                }
            },
            "retina_detect": true
        });
    }

    // Mobil Menü İşlemleri
    const menuToggle = document.querySelector('.menu-toggle');
    const navLinks = document.querySelector('.nav-links');

    // Bölüm içi kaydırma için değişkenler
    let isInSectionScrolling = false;
    let lastScrollTop = 0;

    if (menuToggle && navLinks) {
        menuToggle.addEventListener('click', () => {
            navLinks.classList.toggle('active');

            // İkonu değiştir (hamburger <-> çarpı)
            const icon = menuToggle.querySelector('i');
            if (navLinks.classList.contains('active')) {
                icon.classList.remove('fa-bars');
                icon.classList.add('fa-times');
                menuToggle.setAttribute('aria-label', 'Menüyü Kapat');
            } else {
                icon.classList.remove('fa-times');
                icon.classList.add('fa-bars');
                menuToggle.setAttribute('aria-label', 'Menüyü Aç');
            }
        });

        // Menü linklerine tıklandığında menüyü kapat
        const links = navLinks.querySelectorAll('a');
        links.forEach(link => {
            link.addEventListener('click', () => {
                if (navLinks.classList.contains('active')) {
                    navLinks.classList.remove('active');
                    const icon = menuToggle.querySelector('i');
                    icon.classList.remove('fa-times');
                    icon.classList.add('fa-bars');
                    menuToggle.setAttribute('aria-label', 'Menüyü Aç');
                }
            });
        });
    }

    // Kaydırma sırasında header'a 'scrolled' sınıfını ekleme/kaldırma
    const header = document.querySelector('.header');

    function handleScroll() {
        if (window.pageYOffset > 50) { // Sayfa 50px'den fazla kaydırıldıysa
            header.classList.add('scrolled');
        } else {
            header.classList.remove('scrolled');
        }

        // Back to Top butonunu göster/gizle
        const backToTopButton = document.getElementById('backToTop');
        if (backToTopButton) {
            if (window.pageYOffset > 300) {
                backToTopButton.classList.add('visible');
            } else {
                backToTopButton.classList.remove('visible');
            }
        }
    }

    // Scroll olayını window nesnesine ekle
    window.addEventListener('scroll', handleScroll);

    // Back to Top butonuna tıklama olayı ekle
    const backToTopButton = document.getElementById('backToTop');
    if (backToTopButton) {
        backToTopButton.addEventListener('click', function(e) {
            e.preventDefault();
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });
    }

    // Sayfa Navigasyon Sistemi
    const sections = document.querySelectorAll('.section');
    const pagination = document.querySelectorAll('.pagination-bullet');

    // Sayfa yüklenirken aktif bölümü belirle
    function checkActiveSection() {
        const scrollPosition = window.scrollY + window.innerHeight / 3;
        let activeIndex = 0;

        sections.forEach((section, index) => {
            const sectionTop = section.offsetTop;
            const sectionBottom = sectionTop + section.offsetHeight;

            if (scrollPosition >= sectionTop && scrollPosition < sectionBottom) {
                activeIndex = index;
            }
        });

        updateActiveBullet(activeIndex);
    }

    // Sayfa yüklenince aktif bölümü kontrol et
    checkActiveSection();

    // Kaydırma göstergelerine tıklama olayı ekle
    pagination.forEach((bullet, index) => {
        bullet.addEventListener('click', () => {
            // Sayfa içi navigasyon
            const targetSection = sections[index];
            window.scrollTo({
                top: targetSection.offsetTop - 70, // Header yüksekliği için offset
                behavior: 'smooth'
            });

            // Aktif göstergeyi güncelle
            updateActiveBullet(index);
        });
    });

    // Sayfa kaydırma sırasında aktif bölümü takip etme
    window.addEventListener('scroll', function() {
        // Hangi bölümün görünür olduğunu kontrol et
        let currentSectionIndex = 0;
        const scrollPosition = window.scrollY + window.innerHeight / 3; // Ekranın 1/3'ü kadar offset

        sections.forEach((section, index) => {
            const sectionTop = section.offsetTop;
            const sectionBottom = sectionTop + section.offsetHeight;

            // Eğer kaydırma pozisyonu bölümün içindeyse
            if (scrollPosition >= sectionTop && scrollPosition < sectionBottom) {
                currentSectionIndex = index;
            }
        });

        // Aktif göstergeyi güncelle
        updateActiveBullet(currentSectionIndex);
    });

    // Aktif göstergeyi güncelleme fonksiyonu
    function updateActiveBullet(index) {
        pagination.forEach((bullet, i) => {
            if (i === index) {
                bullet.classList.add('active');
            } else {
                bullet.classList.remove('active');
            }
        });
    }

    // Menü linklerine tıklama olayı ekle
    document.querySelectorAll('.nav-links a').forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('href').substring(1);
            const targetSection = document.getElementById(targetId);

            if (targetSection) {
                window.scrollTo({
                    top: targetSection.offsetTop - 70, // Header yüksekliği için offset
                    behavior: 'smooth'
                });

                // Aktif göstergeyi güncelle
                const targetIndex = Array.from(sections).findIndex(section => section.id === targetId);
                if (targetIndex !== -1) {
                    updateActiveBullet(targetIndex);
                }
            }
        });
    });

    // Sayaç Animasyonu
    const counters = document.querySelectorAll('.counter');
    const counterObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const counter = entry.target;
                const target = parseInt(counter.getAttribute('data-target'));
                let count = 0;
                const updateCounter = () => {
                    const increment = target / 100;
                    if (count < target) {
                        count += increment;
                        counter.innerText = Math.ceil(count);
                        setTimeout(updateCounter, 20);
                    } else {
                        counter.innerText = target;
                    }
                };
                updateCounter();
                observer.unobserve(counter);
            }
        });
    }, { threshold: 0.5 });

    counters.forEach(counter => {
        counterObserver.observe(counter);
    });

    // Özellikler Sekmesi
    const tabButtons = document.querySelectorAll('.tab-btn');
    const tabPanes = document.querySelectorAll('.tab-pane');

    tabButtons.forEach(button => {
        button.addEventListener('click', () => {
            // Aktif sekme butonunu güncelle
            tabButtons.forEach(btn => btn.classList.remove('active'));
            button.classList.add('active');

            // Aktif sekme içeriğini göster
            const tabId = button.getAttribute('data-tab');
            tabPanes.forEach(pane => pane.classList.remove('active'));
            document.getElementById(tabId).classList.add('active');
        });
    });

    // Testimonial Slider
    const testimonialCards = document.querySelectorAll('.testimonial-card');
    const testimonialDots = document.querySelectorAll('.testimonial-dots .dot');
    const prevButton = document.querySelector('.testimonial-btn.prev');
    const nextButton = document.querySelector('.testimonial-btn.next');
    let currentTestimonial = 0;

    function showTestimonial(index) {
        testimonialCards.forEach(card => card.classList.remove('active'));
        testimonialDots.forEach(dot => dot.classList.remove('active'));

        testimonialCards[index].classList.add('active');
        testimonialDots[index].classList.add('active');
        currentTestimonial = index;
    }

    if (prevButton && nextButton) {
        prevButton.addEventListener('click', () => {
            currentTestimonial = (currentTestimonial - 1 + testimonialCards.length) % testimonialCards.length;
            showTestimonial(currentTestimonial);
        });

        nextButton.addEventListener('click', () => {
            currentTestimonial = (currentTestimonial + 1) % testimonialCards.length;
            showTestimonial(currentTestimonial);
        });
    }

    testimonialDots.forEach((dot, index) => {
        dot.addEventListener('click', () => {
            showTestimonial(index);
        });
    });

    // Otomatik testimonial değiştirme
    setInterval(() => {
        if (testimonialCards.length > 0) {
            currentTestimonial = (currentTestimonial + 1) % testimonialCards.length;
            showTestimonial(currentTestimonial);
        }
    }, 5000);

    // --- Fiyatlandırma Toggle İşlevselliği Başlangıcı ---
    const pricingToggleContainer = document.querySelector('.pricing-toggle-container');
    const toggleButtons = document.querySelectorAll('.pricing-toggle-buttons .toggle-btn');
    const pricingSection = document.getElementById('pricing'); // Ana bölüm
    const packagePrices = document.querySelectorAll('.package-price');
    const discountBadge = document.querySelector('.discount-badge');

    function updatePricingView(selectedPeriod) {
        // Buton aktif sınıfını ayarla
        toggleButtons.forEach(btn => {
            btn.classList.remove('active');
            if (btn.dataset.period === selectedPeriod) {
                btn.classList.add('active');
            }
        });

        // Ana bölüme data attribute ekle (CSS hedeflemesi için, örn. indirim rozeti)
        if (pricingSection) {
            pricingSection.dataset.activePeriod = selectedPeriod;
        }

        // Fiyatları güncelle (display stilini değiştirerek)
        packagePrices.forEach(priceDiv => {
            const monthlyPrice = priceDiv.querySelector('.monthly');
            const yearlyPrice = priceDiv.querySelector('.yearly');

            if (monthlyPrice && yearlyPrice) {
                if (selectedPeriod === 'monthly') {
                    monthlyPrice.style.display = 'inline-block';
                    yearlyPrice.style.display = 'none';
                } else { // yearly
                    monthlyPrice.style.display = 'none';
                    yearlyPrice.style.display = 'inline-block';
                }
            }
        });

        // İndirim rozetinin görünürlüğünü CSS'e bırakıyoruz (data-active-period ile)
    }

    // Butonlara tıklama olayı ekle
    toggleButtons.forEach(button => {
        button.addEventListener('click', () => {
            const selectedPeriod = button.dataset.period;
            updatePricingView(selectedPeriod);
        });
    });

    // Başlangıç durumunu ayarla (HTML'deki 'active' sınıfına göre)
    const initialActiveButton = document.querySelector('.pricing-toggle-buttons .toggle-btn.active');
    if (initialActiveButton) {
        updatePricingView(initialActiveButton.dataset.period);
    } else if (toggleButtons.length > 0) {
        // Eğer HTML'de active sınıfı yoksa, ilk butonu aktif yap (veya varsayılanı 'monthly')
        const defaultPeriod = 'monthly'; // Varsayılanı aylık yap
        const defaultButton = Array.from(toggleButtons).find(btn => btn.dataset.period === defaultPeriod) || toggleButtons[0];
         updatePricingView(defaultButton.dataset.period);
    }
    // --- Fiyatlandırma Toggle İşlevselliği Sonu ---

    // SSS Accordion - Geliştirilmiş
    const faqItems = document.querySelectorAll('.faq-item');

    // Sayfa yüklendiğinde tüm soruları kapalı hale getir (daha güçlü yöntem)
    function closeFaqItems() {
        faqItems.forEach(item => {
            item.classList.remove('active');
        });
    }

    // Sayfa yüklendiğinde çağır
    closeFaqItems();

    // AOS animasyonlarından sonra tekrar çağır
    setTimeout(closeFaqItems, 500);

    // Sayfa tamamen yüklendiğinde tekrar çağır
    window.addEventListener('load', closeFaqItems);

    faqItems.forEach(item => {
        const question = item.querySelector('.faq-question');

        question.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();

            // Diğer tüm açık SSS öğelerini kapat
            faqItems.forEach(otherItem => {
                if (otherItem !== item && otherItem.classList.contains('active')) {
                    otherItem.classList.remove('active');
                }
            });

            // Tıklanan öğeyi aç/kapat
            item.classList.toggle('active');
        });
    });

    // Yeni SSS Accordion
    const newFaqItems = document.querySelectorAll('.new-faq-item');

    // Sayfa yüklendiğinde tüm yeni soruları kapalı hale getir
    function closeAllNewFaqs() {
        newFaqItems.forEach(item => {
            item.classList.remove('active');
        });
    }

    // Sayfa yüklendiğinde çağır
    closeAllNewFaqs();

    // AOS animasyonlarından sonra tekrar çağır
    setTimeout(closeAllNewFaqs, 500);

    // Sayfa tamamen yüklendiğinde tekrar çağır
    window.addEventListener('load', closeAllNewFaqs);

    // Her yeni soru için tıklama olayı ekle
    newFaqItems.forEach(item => {
        const question = item.querySelector('.new-faq-question');

        question.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            // Diğer tüm açık yeni SSS öğelerini kapat
            newFaqItems.forEach(otherItem => {
                if (otherItem !== item && otherItem.classList.contains('active')) {
                    otherItem.classList.remove('active');
                }
            });

            // Tıklanan öğeyi aç/kapat
            item.classList.toggle('active');
        });
    });

    // İletişim Formu Gönderimi
    const contactForm = document.querySelector('.contact-form');

    if (contactForm) {
        contactForm.addEventListener('submit', function(e) {
            e.preventDefault();

            // Form verilerini al
            const formData = new FormData(contactForm);
            const formValues = {};

            for (let [key, value] of formData.entries()) {
                formValues[key] = value;
            }

            // Normalde burada bir AJAX isteği yapılır
            console.log('Form verileri:', formValues);

            // Başarılı gönderim mesajı (gerçek uygulamada AJAX başarılı yanıtından sonra gösterilir)
            alert('Demo talebiniz başarıyla alındı! En kısa sürede sizinle iletişime geçeceğiz.');

            // Formu sıfırla
            contactForm.reset();
        });
    }
});
