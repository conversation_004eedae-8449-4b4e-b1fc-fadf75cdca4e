import { createRequire } from 'module';const require = createRequire(import.meta.url);
import {
  AngularAppEngine,
  InlineCriticalCssProcessor,
  PrerenderFallback,
  RenderMode,
  createRequestHandler,
  destroyAngularServerApp,
  extractRoutesAndCreateRouteTree,
  getOrCreateAngularServerApp,
  getRoutesFromAngularRouterConfig,
  provideServerRoutesConfig,
  provideServerRouting,
  setAngularAppEngineManifest,
  setAngularAppManifest,
  withAppShell
} from "./chunk-65ATSLLC.js";
import "./chunk-YVI6OUFV.js";
import "./chunk-YUGLMLOQ.js";
import "./chunk-ZZ77OLRI.js";
import "./chunk-VEX7FQJS.js";
import "./chunk-LKYZIEE2.js";
import "./chunk-OTJ7UO3I.js";
import "./chunk-GRDOIMAR.js";
import "./chunk-NNB67BKT.js";
import "./chunk-HGVHWTGE.js";
import "./chunk-EXQLYBKH.js";
import "./chunk-IUOK4BIQ.js";
import "./chunk-GBTWTWDP.js";
export {
  AngularAppEngine,
  PrerenderFallback,
  RenderMode,
  createRequestHandler,
  provideServerRoutesConfig,
  provideServerRouting,
  withAppShell,
  InlineCriticalCssProcessor as ɵInlineCriticalCssProcessor,
  destroyAngularServerApp as ɵdestroyAngularServerApp,
  extractRoutesAndCreateRouteTree as ɵextractRoutesAndCreateRouteTree,
  getOrCreateAngularServerApp as ɵgetOrCreateAngularServerApp,
  getRoutesFromAngularRouterConfig as ɵgetRoutesFromAngularRouterConfig,
  setAngularAppEngineManifest as ɵsetAngularAppEngineManifest,
  setAngularAppManifest as ɵsetAngularAppManifest
};
