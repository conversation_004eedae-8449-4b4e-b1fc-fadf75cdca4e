using System.Collections.Generic;

namespace Core.CrossCuttingConcerns.Caching.KeyGeneration
{
    public interface ICacheKeyGenerator
    {
        /// <summary>
        /// Metod çağrısı için cache key oluşturur
        /// </summary>
        string GenerateKey(int tenantId, string className, string methodName, object[] arguments);
        
        /// <summary>
        /// Entity bazlı cache key oluşturur
        /// </summary>
        string GenerateEntityKey(int tenantId, string entityName, string operation, Dictionary<string, object> parameters = null);
        
        /// <summary>
        /// Cache tag'leri oluşturur
        /// </summary>
        string[] GenerateTags(int tenantId, string entityName, string[] additionalTags = null);
        
        /// <summary>
        /// Pattern bazlı key oluşturur (cache temizleme için)
        /// </summary>
        string GeneratePattern(int tenantId, string entityName = null, string operation = null);
        
        /// <summary>
        /// Key'den tenant ID'sini çıkarır
        /// </summary>
        int ExtractTenantId(string key);
        
        /// <summary>
        /// Key'den entity name'i çıkarır
        /// </summary>
        string ExtractEntityName(string key);
    }
}
