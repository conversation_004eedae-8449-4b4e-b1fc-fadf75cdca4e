using Business.Abstract;
using Core.Utilities.Results;
using Core.CrossCuttingConcerns.Caching;
using Business.BusinessAscpects;
using Core.Aspects.Autofac.Logging;
using Core.Aspects.Autofac.Performance;
using System.Security.Cryptography;
using System.Text;

namespace Business.Concrete
{
    public class AdvancedRateLimitManager : IAdvancedRateLimitService
    {
        private readonly ICacheManager _cacheManager;

        // Login Rate Limiting Constants
        private const int MAX_LOGIN_ATTEMPTS = 5;
        private readonly int[] LOGIN_BAN_DURATIONS = { 5, 15, 60, 360, 1440 }; // dakika: 5dk, 15dk, 1saat, 6saat, 24saat

        // Register Rate Limiting Constants
        private const int MAX_REGISTER_ATTEMPTS = 3;
        private readonly int[] REGISTER_BAN_DURATIONS = { 60, 360, 1440, 10080 }; // dakika: 1saat, 6saat, 24saat, 7gün

        // Profile Image Upload Rate Limiting Constants
        private const int MAX_PROFILE_IMAGE_UPLOADS_PER_DAY = 3;

        // File Download Rate Limiting Constants
        private const int MAX_FILE_DOWNLOADS_PER_10_MINUTES = 5;

        public AdvancedRateLimitManager(ICacheManager cacheManager)
        {
            _cacheManager = cacheManager;
        }

        [LogAspect]
        [PerformanceAspect(3)]
        public IResult CheckLoginAttempt(string ipAddress, string deviceFingerprint)
        {
            // Sadece device fingerprint tabanlı ban kontrolü
            // IP tabanlı ban kullanmıyoruz çünkü paylaşılan IP'lerde (spor salonu WiFi vb.)
            // diğer kullanıcıları da etkileyebilir
            var banKey = GetLoginBanKey(ipAddress, deviceFingerprint);

            if (_cacheManager.IsAdd(banKey))
            {
                var banEndTime = _cacheManager.Get<DateTime>(banKey);
                var remaining = banEndTime - DateTime.Now;

                if (remaining.TotalMinutes > 0)
                {
                    var remainingMinutes = Math.Ceiling(remaining.TotalMinutes);
                    return new ErrorResult($"Çok fazla başarısız giriş denemesi. Lütfen {remainingMinutes} dakika sonra tekrar deneyin.");
                }
                else
                {
                    // Ban süresi dolmuş, cache'den temizle
                    _cacheManager.Remove(banKey);
                }
            }

            return new SuccessResult();
        }

        [LogAspect]
        [PerformanceAspect(3)]
        public IResult RecordFailedLogin(string ipAddress, string deviceFingerprint)
        {
            var failKey = GetLoginFailKey(ipAddress, deviceFingerprint);
            var banKey = GetLoginBanKey(ipAddress, deviceFingerprint);
            var violationKey = GetLoginViolationKey(ipAddress, deviceFingerprint);

            int failCount = 0;
            if (_cacheManager.IsAdd(failKey))
            {
                failCount = _cacheManager.Get<int>(failKey);
            }
            failCount++;

            if (failCount >= MAX_LOGIN_ATTEMPTS)
            {
                // Violation sayısını al
                int violationCount = 0;
                if (_cacheManager.IsAdd(violationKey))
                {
                    violationCount = _cacheManager.Get<int>(violationKey);
                }

                // Progressive ban süresi hesapla
                int banDuration = GetProgressiveBanDuration(violationCount, LOGIN_BAN_DURATIONS);
                var banEndTime = DateTime.Now.AddMinutes(banDuration);



                // Sadece device fingerprint tabanlı ban uygula
                _cacheManager.Add(banKey, banEndTime, banDuration);

                // Violation sayısını artır
                violationCount++;
                _cacheManager.Add(violationKey, violationCount, 10080); // 7 gün boyunca violation sayısını tut

                // Fail count'u sıfırla
                _cacheManager.Remove(failKey);



                return new ErrorResult($"Çok fazla başarısız giriş denemesi. {banDuration} dakika boyunca giriş yapamazsınız.");
            }
            else
            {
                // Fail count'u güncelle (5 dakika boyunca tut)
                _cacheManager.Add(failKey, failCount, 5);

            }

            return new SuccessResult();
        }

        [LogAspect]
        [PerformanceAspect(3)]
        public IResult RecordSuccessfulLogin(string ipAddress, string deviceFingerprint)
        {
            var failKey = GetLoginFailKey(ipAddress, deviceFingerprint);
            var banKey = GetLoginBanKey(ipAddress, deviceFingerprint);

            // Başarılı giriş sonrası sadece device fingerprint tabanlı fail count'u ve ban'ı sıfırla
            _cacheManager.Remove(failKey);
            _cacheManager.Remove(banKey);

            return new SuccessResult();
        }

        [LogAspect]
        [PerformanceAspect(3)]
        public IDataResult<int> GetRemainingLoginBanTime(string ipAddress, string deviceFingerprint)
        {
            var banKey = GetLoginBanKey(ipAddress, deviceFingerprint);



            if (_cacheManager.IsAdd(banKey))
            {
                var banEndTime = _cacheManager.Get<DateTime>(banKey);
                var remaining = banEndTime - DateTime.Now;



                if (remaining.TotalMinutes > 0)
                {
                    return new SuccessDataResult<int>((int)Math.Ceiling(remaining.TotalMinutes));
                }
                else
                {
                    // Ban süresi dolmuş, cache'den temizle
                    _cacheManager.Remove(banKey);

                }
            }
            else
            {

            }

            return new SuccessDataResult<int>(0);
        }

        [LogAspect]
        [PerformanceAspect(3)]
        public IResult CheckRegisterAttempt(string ipAddress)
        {
            var banKey = GetRegisterBanKey(ipAddress);

            if (_cacheManager.IsAdd(banKey))
            {
                var banEndTime = _cacheManager.Get<DateTime>(banKey);
                var remaining = banEndTime - DateTime.Now;

                if (remaining.TotalMinutes > 0)
                {
                    var remainingText = remaining.TotalDays >= 1
                        ? $"{Math.Ceiling(remaining.TotalDays)} gün"
                        : $"{Math.Ceiling(remaining.TotalMinutes)} dakika";

                    return new ErrorResult($"Çok fazla kayıt denemesi. Lütfen {remainingText} sonra tekrar deneyin.");
                }
                else
                {
                    _cacheManager.Remove(banKey);
                }
            }

            return new SuccessResult();
        }

        [LogAspect]
        [PerformanceAspect(3)]
        public IResult RecordSuccessfulRegister(string ipAddress)
        {
            var countKey = GetRegisterCountKey(ipAddress);
            var banKey = GetRegisterBanKey(ipAddress);
            var violationKey = GetRegisterViolationKey(ipAddress);

            int registerCount = 0;
            if (_cacheManager.IsAdd(countKey))
            {
                registerCount = _cacheManager.Get<int>(countKey);
            }
            registerCount++;

            if (registerCount >= MAX_REGISTER_ATTEMPTS)
            {
                // Violation sayısını al
                int violationCount = 0;
                if (_cacheManager.IsAdd(violationKey))
                {
                    violationCount = _cacheManager.Get<int>(violationKey);
                }

                // Progressive ban süresi hesapla
                int banDuration = GetProgressiveBanDuration(violationCount, REGISTER_BAN_DURATIONS);
                var banEndTime = DateTime.Now.AddMinutes(banDuration);

                // Ban uygula
                _cacheManager.Add(banKey, banEndTime, banDuration);

                // Violation sayısını artır
                violationCount++;
                _cacheManager.Add(violationKey, violationCount, 20160); // 14 gün boyunca violation sayısını tut

                // Register count'u sıfırla
                _cacheManager.Remove(countKey);

                var banText = banDuration >= 1440
                    ? $"{banDuration / 1440} gün"
                    : $"{banDuration / 60} saat";

                return new ErrorResult($"Günlük kayıt limitini aştınız. {banText} boyunca kayıt yapamazsınız.");
            }
            else
            {
                // Register count'u güncelle (24 saat boyunca tut)
                _cacheManager.Add(countKey, registerCount, 1440);
            }

            return new SuccessResult();
        }

        [LogAspect]
        [PerformanceAspect(3)]
        public IDataResult<int> GetRemainingRegisterBanTime(string ipAddress)
        {
            var banKey = GetRegisterBanKey(ipAddress);

            if (_cacheManager.IsAdd(banKey))
            {
                var banEndTime = _cacheManager.Get<DateTime>(banKey);
                var remaining = banEndTime - DateTime.Now;

                if (remaining.TotalMinutes > 0)
                {
                    return new SuccessDataResult<int>((int)Math.Ceiling(remaining.TotalMinutes));
                }
            }

            return new SuccessDataResult<int>(0);
        }

        [LogAspect]
        [PerformanceAspect(3)]
        public IResult CheckProfileImageUploadAttempt(int userId)
        {
            var uploadCountKey = GetProfileImageUploadCountKey(userId);

            int uploadCount = 0;
            if (_cacheManager.IsAdd(uploadCountKey))
            {
                uploadCount = _cacheManager.Get<int>(uploadCountKey);
            }

            if (uploadCount >= MAX_PROFILE_IMAGE_UPLOADS_PER_DAY)
            {
                return new ErrorResult($"Günlük profil fotoğrafı yükleme limitini aştınız. Günde en fazla {MAX_PROFILE_IMAGE_UPLOADS_PER_DAY} fotoğraf yükleyebilirsiniz.");
            }

            return new SuccessResult();
        }

        [LogAspect]
        [PerformanceAspect(3)]
        public IResult RecordProfileImageUpload(int userId)
        {
            var uploadCountKey = GetProfileImageUploadCountKey(userId);

            int uploadCount = 0;
            if (_cacheManager.IsAdd(uploadCountKey))
            {
                uploadCount = _cacheManager.Get<int>(uploadCountKey);
            }
            uploadCount++;

            if (uploadCount > MAX_PROFILE_IMAGE_UPLOADS_PER_DAY)
            {
                return new ErrorResult($"Günlük profil fotoğrafı yükleme limitini aştınız. Günde en fazla {MAX_PROFILE_IMAGE_UPLOADS_PER_DAY} fotoğraf yükleyebilirsiniz.");
            }

            // Günün sonuna kadar cache'te tut (gece yarısına kadar)
            var endOfDay = DateTime.Today.AddDays(1);
            var minutesUntilEndOfDay = (int)(endOfDay - DateTime.Now).TotalMinutes;

            _cacheManager.Add(uploadCountKey, uploadCount, minutesUntilEndOfDay);

            return new SuccessResult();
        }

        [LogAspect]
        [PerformanceAspect(3)]
        public IDataResult<int> GetRemainingProfileImageUploads(int userId)
        {
            var uploadCountKey = GetProfileImageUploadCountKey(userId);

            int uploadCount = 0;
            if (_cacheManager.IsAdd(uploadCountKey))
            {
                uploadCount = _cacheManager.Get<int>(uploadCountKey);
            }

            int remaining = MAX_PROFILE_IMAGE_UPLOADS_PER_DAY - uploadCount;
            return new SuccessDataResult<int>(Math.Max(0, remaining));
        }

        [LogAspect]
        [PerformanceAspect(3)]
        public IResult CheckFileDownloadAttempt(int userId)
        {
            var downloadCountKey = GetFileDownloadCountKey(userId);

            int downloadCount = 0;
            if (_cacheManager.IsAdd(downloadCountKey))
            {
                downloadCount = _cacheManager.Get<int>(downloadCountKey);
            }

            if (downloadCount >= MAX_FILE_DOWNLOADS_PER_10_MINUTES)
            {
                return new ErrorResult($"Dosya indirme limitini aştınız. 10 dakikada en fazla {MAX_FILE_DOWNLOADS_PER_10_MINUTES} dosya indirebilirsiniz.");
            }

            return new SuccessResult();
        }

        [LogAspect]
        [PerformanceAspect(3)]
        public IResult RecordFileDownload(int userId)
        {
            var downloadCountKey = GetFileDownloadCountKey(userId);

            int downloadCount = 0;
            if (_cacheManager.IsAdd(downloadCountKey))
            {
                downloadCount = _cacheManager.Get<int>(downloadCountKey);
            }
            downloadCount++;

            if (downloadCount > MAX_FILE_DOWNLOADS_PER_10_MINUTES)
            {
                return new ErrorResult($"Dosya indirme limitini aştınız. 10 dakikada en fazla {MAX_FILE_DOWNLOADS_PER_10_MINUTES} dosya indirebilirsiniz.");
            }

            // 10 dakika boyunca cache'te tut
            _cacheManager.Add(downloadCountKey, downloadCount, 10);

            return new SuccessResult();
        }

        [LogAspect]
        [PerformanceAspect(3)]
        public IDataResult<int> GetRemainingFileDownloads(int userId)
        {
            var downloadCountKey = GetFileDownloadCountKey(userId);

            int downloadCount = 0;
            if (_cacheManager.IsAdd(downloadCountKey))
            {
                downloadCount = _cacheManager.Get<int>(downloadCountKey);
            }

            int remaining = MAX_FILE_DOWNLOADS_PER_10_MINUTES - downloadCount;
            return new SuccessDataResult<int>(Math.Max(0, remaining));
        }

        public string GenerateDeviceFingerprint(string ipAddress, string userAgent, string deviceInfo)
        {
            var combinedInfo = $"{ipAddress}|{userAgent}|{deviceInfo}";
            using (var sha256 = SHA256.Create())
            {
                var hashBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(combinedInfo));
                return Convert.ToBase64String(hashBytes);
            }
        }

        // Private Helper Methods
        private int GetProgressiveBanDuration(int violationCount, int[] banDurations)
        {
            if (violationCount >= banDurations.Length)
            {
                return banDurations[banDurations.Length - 1]; // Son değeri kullan
            }
            return banDurations[violationCount];
        }

        private string GetLoginFailKey(string ipAddress, string deviceFingerprint)
        {
            return $"login_fail:{ipAddress}:{deviceFingerprint}";
        }

        private string GetLoginBanKey(string ipAddress, string deviceFingerprint)
        {
            return $"login_ban:{ipAddress}:{deviceFingerprint}";
        }

        private string GetLoginViolationKey(string ipAddress, string deviceFingerprint)
        {
            return $"login_violation:{ipAddress}:{deviceFingerprint}";
        }

        private string GetRegisterCountKey(string ipAddress)
        {
            var today = DateTime.Now.ToString("yyyy-MM-dd");
            return $"register_count:{ipAddress}:{today}";
        }

        private string GetRegisterBanKey(string ipAddress)
        {
            return $"register_ban:{ipAddress}";
        }

        private string GetRegisterViolationKey(string ipAddress)
        {
            return $"register_violation:{ipAddress}";
        }

        private string GetProfileImageUploadCountKey(int userId)
        {
            var today = DateTime.Now.ToString("yyyy-MM-dd");
            return $"profile_image_upload:{userId}:{today}";
        }

        private string GetFileDownloadCountKey(int userId)
        {
            var tenMinuteSlot = DateTime.Now.ToString("yyyy-MM-dd-HH") + "-" + (DateTime.Now.Minute / 10);
            return $"file_download:{userId}:{tenMinuteSlot}";
        }
    }
}
