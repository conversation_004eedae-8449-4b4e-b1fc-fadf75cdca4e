﻿﻿﻿﻿using System;
using System.IO;
using Newtonsoft.Json;

namespace Core.CrossCuttingConcerns.Logging.FileLogger
{
    public class FileLoggerService : ILogService
    {
        private readonly string _logDirectory;
        private readonly object _lock = new object();

        public FileLoggerService()
        {
            // Ana log klasörü
            _logDirectory = @"C:\GymProjectLogs";
            Directory.CreateDirectory(_logDirectory);
        }

        private void Log(string level, string message)
        {
            try
            {
                // Kullanıcı ID'sini mesajdan almaya çalış (JSON içinden)
                string userId = ExtractUserIdFromMessage(message);

                // Kullanıcı ID'si yoksa Anonymous kullan
                userId = string.IsNullOrEmpty(userId) ? "Anonymous" : userId;

                // Kullanıcı klasörü
                var dateDirectory = Path.Combine(_logDirectory, "Users", userId);
                Directory.CreateDirectory(dateDirectory);

                var logFilePath = Path.Combine(dateDirectory, DateTime.Now.ToString("dd.MM.yyyy") + ".txt");


                // Mesajı daha okunabilir hale getirmek için JSON parse ve formatlama
                string formattedMessage = FormatLogMessage(message);

                var logMessage = $"{DateTime.Now:yyyy-MM-dd HH:mm:ss.fff} [{level}]\n{formattedMessage}\n";

                lock (_lock)
                {
                    File.AppendAllText(logFilePath, logMessage + Environment.NewLine);
                }
            }
            catch
            {
                // Loglama sırasında hata oluşursa sessizce devam et
            }
        }

        private string ExtractUserIdFromMessage(string message)
        {
            try
            {
                // JSON mesajını deserialize et
                var logDetail = JsonConvert.DeserializeObject<dynamic>(message);

                // User alanını al
                string userId = logDetail?.User?.ToString();

                if (string.IsNullOrWhiteSpace(userId))
                    return null;

                return userId;
            }
            catch
            {
                return null;
            }
        }

        private string FormatLogMessage(string message)
        {
            try
            {
                // JSON'u dinamik olarak deserialize et
                var logDetail = JsonConvert.DeserializeObject<dynamic>(message);

                // Şifre gibi hassas alanları maskele
                MaskSensitiveData(logDetail);

                // Önemli alanları okunabilir şekilde stringe çevir
                string fullName = logDetail?.FullName ?? "N/A";
                string methodName = logDetail?.MethodName ?? "N/A";
                string user = logDetail?.User ?? "Anonymous";
                string exceptionMessage = logDetail?.ExceptionMessage ?? "None";
                string logDate = logDetail?.LogDate ?? DateTime.Now.ToString();

                // Parametreleri formatlı JSON string olarak al
                string parameters = "";
                if (logDetail?.Parameters != null)
                {
                    parameters = JsonConvert.SerializeObject(logDetail.Parameters, Formatting.Indented);
                }

                // Daha okunabilir log mesajı oluştur
                string formatted =
$@"FullName        : {fullName}
MethodName      : {methodName}
User            : {user}
ExceptionMessage: {exceptionMessage}
LogDate         : {logDate}
Parameters      : {parameters}";

                return formatted;
            }
            catch
            {
                // Formatlama başarısız olursa orijinal mesajı döndür
                return message;
            }
        }

        private void MaskSensitiveData(dynamic logDetail)
        {
            try
            {
                if (logDetail?.Parameters != null)
                {
                    foreach (var param in logDetail.Parameters)
                    {
                        if (param?.Name != null && param.Name.ToString().ToLower().Contains("password"))
                        {
                            if (param.Value != null)
                            {
                                // Şifreyi maskele
                                param.Value = "****";
                            }
                        }
                        else if (param?.Value != null && param.Value is Newtonsoft.Json.Linq.JObject)
                        {
                            // İç içe nesnelerde de şifre varsa maskele
                            MaskSensitiveData(param.Value);
                        }
                    }
                }
            }
            catch
            {
                // Hata olursa sessizce devam et
            }
        }

        public void Info(string message, bool isPerformanceLog = false)
        {
            Log("INFO", message);
        }

        public void Debug(string message)
        {
            Log("DEBUG", message);
        }

        public void Warn(string message)
        {
            Log("WARN", message);
        }

        public void Error(string message)
        {
            Log("ERROR", message);
        }

        public void Fatal(string message)
        {
            Log("FATAL", message);
        }

        public void LogPerformance(string message)
        {
            Log("PERFORMANCE", message);
        }
    }
}
