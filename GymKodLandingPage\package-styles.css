/* Yeni Paket Kutuları Stili */
.package-boxes-container {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 2rem;
    margin: 3rem 0;
}

.package-box {
    flex: 1;
    min-width: 280px;
    max-width: 350px;
    background-color: var(--bg-dark-tertiary);
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 5px 25px rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
    position: relative;
    display: flex;
    flex-direction: column;
}

.package-box:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
    border-color: var(--primary-color);
}

.package-header {
    padding: 1.5rem;
    text-align: center;
    border-bottom: 1px solid var(--border-color-dark);
    position: relative;
    background-color: var(--bg-dark-quaternary);
}

.package-header h3 {
    font-size: 1.8rem;
    margin-bottom: 0.5rem;
    color: var(--light-color);
}

.package-price {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin: 1rem 0;
    display: flex;
    align-items: center;
    justify-content: center;
}

.period {
    font-size: 1rem;
    color: var(--text-color-dark-secondary);
    margin-left: 0.2rem;
}

.package-description {
    font-size: 1rem;
    color: var(--text-color-dark-secondary);
    margin-bottom: 0.5rem;
}

.package-features {
    padding: 1.5rem;
    flex-grow: 1;
}

.package-feature {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
    color: var(--text-color-dark);
}

.package-feature i {
    color: var(--primary-color);
    margin-right: 0.8rem;
    font-size: 1.1rem;
    min-width: 20px;
}

.package-feature.disabled {
    color: var(--text-color-dark-secondary);
    opacity: 0.6;
}

.package-feature.disabled i {
    color: var(--text-color-dark-secondary);
}

.package-footer {
    padding: 1.5rem;
    text-align: center;
}

.package-btn {
    display: inline-block;
    padding: 0.8rem 2rem;
    background-color: var(--primary-color);
    color: var(--light-color);
    border-radius: var(--border-radius);
    font-weight: 600;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    width: 100%;
}

.package-btn:hover {
    background-color: var(--secondary-color);
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(67, 97, 238, 0.3);
}

.package-highlight {
    position: absolute;
    top: 0;
    right: 0;
    background-color: var(--primary-color);
    color: var(--bg-dark);
    padding: 0.5rem 1rem;
    font-size: 0.8rem;
    font-weight: 600;
    border-bottom-left-radius: var(--border-radius);
}

/* Responsive Tasarım */
@media (max-width: 992px) {
    .package-boxes-container {
        gap: 1.5rem;
    }

    .package-box {
        min-width: 260px;
    }

    .package-header h3 {
        font-size: 1.6rem;
    }

    .package-price {
        font-size: 2.2rem;
    }
}

@media (max-width: 768px) {
    .package-boxes-container {
        flex-direction: column;
        align-items: center;
    }

    .package-box {
        width: 100%;
        max-width: 450px;
        margin-bottom: 1.5rem;
    }
}

/* Fiyatlandırma Toggle Butonları */
.pricing-toggle-container {
    display: block; /* Flex yerine block */
    text-align: center; /* İçindeki inline-flex öğeyi ortala */
    margin-bottom: 2.5rem; /* Paket kutularıyla arasına boşluk artırıldı */
    /* flex-wrap ve gap flex ile ilgili olduğu için kaldırıldı */
    /* align-items kaldırıldı */
}

.pricing-toggle-buttons {
    display: inline-flex; /* Butonları yan yana tut */
    background-color: var(--bg-dark-secondary); /* Koyu gri arka plan (resimdeki gibi) */
    border-radius: var(--border-radius-pill); /* Tam yuvarlak köşeler */
    padding: 6px; /* İç boşluk */
    border: 1px solid var(--border-color-dark); /* Hafif kenarlık */
}

.toggle-btn {
    background-color: transparent; /* Varsayılan arka planı kaldır */
    border: none; /* Varsayılan kenarlığı kaldır */
    color: var(--text-color-dark-secondary); /* Soluk metin rengi */
    padding: 0.7rem 1.8rem; /* Buton içi boşluk (biraz daha geniş) */
    border-radius: var(--border-radius-pill); /* Tam yuvarlak köşeler */
    cursor: pointer;
    font-weight: 600; /* Biraz daha kalın font */
    font-size: 0.9rem; /* Font boyutu */
    transition: background-color var(--transition-speed-fast) ease, color var(--transition-speed-fast) ease;
    white-space: nowrap; /* Metnin kırılmasını önle */
    line-height: 1; /* Dikey hizalama için */
}

.toggle-btn.active {
    background-color: var(--bg-dark-quaternary); /* Aktif buton için daha açık gri (resimdeki gibi) */
    color: var(--text-color-dark); /* Aktif buton metni daha belirgin */
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.25); /* Hafif iç gölge */
}

.toggle-btn:not(.active):hover {
    color: var(--text-color-dark); /* Hover'da metni biraz belirginleştir */
}

.pricing-toggle-info {
    text-align: center;
}

.discount-badge {
    background-color: var(--primary-color-light);
    color: var(--primary-color);
    padding: 0.4rem 0.9rem;
    border-radius: var(--border-radius);
    font-size: 0.8rem;
    font-weight: 500;
    display: inline-block;
    opacity: 0; /* Başlangıçta gizli */
    transition: opacity var(--transition-speed) ease; /* Daha yavaş geçiş */
    margin-left: 1rem; /* Butonlardan biraz ayır */
}

/* Yıllık seçiliyken indirim rozetini göster */
.pricing-section[data-active-period="yearly"] .discount-badge {
    opacity: 1;
}

/* Fiyat Gösterimi (JS ile kontrol edilecek, CSS sadece başlangıç durumu ve geçiş için) */
.package-price .monthly,
.package-price .yearly {
    transition: opacity var(--transition-speed-fast) ease, transform var(--transition-speed-fast) ease;
    display: inline-block; /* Yan yana durmaları için */
}

/* Başlangıçta yıllık fiyatları gizle (HTML'deki style="display: none;" kaldırılabilir) */
.package-price .yearly {
     display: none;
}
