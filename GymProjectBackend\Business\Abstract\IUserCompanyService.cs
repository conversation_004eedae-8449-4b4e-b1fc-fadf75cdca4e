﻿using Core.Utilities.Results;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Business.Abstract
{
    public interface IUserCompanyService
    {
        IDataResult<List<UserCompany>> GetAll();
        IResult Add(UserCompany userCompany);
        IResult Update(UserCompany userCompany);
        IResult Delete(int id);
        IDataResult<List<UserCompanyDetailDto>> GetUserCompanyDetails();
        IDataResult<int> GetUserCompanyId(int userId);
        IDataResult<List<UserCompany>> GetUserCompanies(int userId);
        IResult UpdateActiveCompany(int userId, int companyId);
    }
}
