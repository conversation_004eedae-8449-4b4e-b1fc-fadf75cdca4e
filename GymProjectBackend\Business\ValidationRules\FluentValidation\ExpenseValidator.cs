using Entities.Concrete;
using FluentValidation;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Business.ValidationRules.FluentValidation
{
    public class ExpenseValidator : AbstractValidator<Expense>
    {
        public ExpenseValidator()
        {
            // RuleFor(e => e.Description).NotEmpty().WithMessage("Açıklama boş bırakılamaz."); // Zorunluluk tekrar kaldırıldı (isteğe bağlı)
            RuleFor(e => e.Description).MaximumLength(500).WithMessage("Açıklama en fazla 500 karakter olabilir."); // Maksimum uzunluk kuralı kalabilir

            RuleFor(e => e.Amount).NotEmpty().WithMessage("Tutar boş bırakılamaz.");
            RuleFor(e => e.Amount).GreaterThan(0).WithMessage("Tutar 0'dan bü<PERSON>ük olmalıdır."); // Business rule ile çakışabilir ama validator'da da olması iyi

            RuleFor(e => e.ExpenseDate).NotEmpty().WithMessage("Gider tarihi boş bırakılamaz.");

            RuleFor(e => e.ExpenseType).MaximumLength(100).WithMessage("Gider türü en fazla 100 karakter olabilir."); // Eğer girildiyse

            // CompanyID otomatik atanacağı için burada validasyona gerek yok.
        }
    }
}