using Business.Abstract;
using Business.BusinessAscpects.Autofac;
using Business.Constants;
using Core.CrossCuttingConcerns.Caching;
using Core.CrossCuttingConcerns.Caching.Models;
using Core.Utilities.Results;
using System;
using System.Collections.Generic;

namespace Business.Concrete
{
    public class CacheManager : ICacheService
    {
        private readonly ICacheManager _cacheManager;

        public CacheManager(ICacheManager cacheManager)
        {
            _cacheManager = cacheManager;
        }

        [SecuredOperation("owner")]
        public IDataResult<CacheStatistics> GetStatistics()
        {
            try
            {
                var statistics = _cacheManager.GetStatistics();
                return new SuccessDataResult<CacheStatistics>(statistics, "Cache istatistikleri başarıyla getirildi");
            }
            catch (Exception ex)
            {
                return new ErrorDataResult<CacheStatistics>($"Cache istatistikleri alınırken hata oluştu: {ex.Message}");
            }
        }

        [SecuredOperation("owner")]
        public IDataResult<Dictionary<string, object>> GetHealthInfo()
        {
            try
            {
                var healthInfo = _cacheManager.GetHealthInfo();
                return new SuccessDataResult<Dictionary<string, object>>(healthInfo, "Cache sağlık bilgileri başarıyla getirildi");
            }
            catch (Exception ex)
            {
                return new ErrorDataResult<Dictionary<string, object>>($"Cache sağlık bilgileri alınırken hata oluştu: {ex.Message}");
            }
        }

        [SecuredOperation("owner")]
        public IDataResult<string[]> GetAllKeys()
        {
            try
            {
                var keys = _cacheManager.GetAllKeys();
                return new SuccessDataResult<string[]>(keys, $"Toplam {keys.Length} cache anahtarı bulundu");
            }
            catch (Exception ex)
            {
                return new ErrorDataResult<string[]>($"Cache anahtarları alınırken hata oluştu: {ex.Message}");
            }
        }

        [SecuredOperation("owner")]
        public IDataResult<string[]> GetKeysByPattern(string pattern)
        {
            try
            {
                var keys = _cacheManager.GetKeysByPattern(pattern);
                return new SuccessDataResult<string[]>(keys, $"Pattern '{pattern}' ile {keys.Length} cache anahtarı bulundu");
            }
            catch (Exception ex)
            {
                return new ErrorDataResult<string[]>($"Pattern ile cache anahtarları alınırken hata oluştu: {ex.Message}");
            }
        }

        [SecuredOperation("owner")]
        public IDataResult<long> GetCacheSize()
        {
            try
            {
                var size = _cacheManager.GetCacheSize();
                return new SuccessDataResult<long>(size, "Cache boyutu başarıyla getirildi");
            }
            catch (Exception ex)
            {
                return new ErrorDataResult<long>($"Cache boyutu alınırken hata oluştu: {ex.Message}");
            }
        }

        [SecuredOperation("owner")]
        public IResult ClearAll()
        {
            try
            {
                _cacheManager.Clear();
                return new SuccessResult("Tüm cache başarıyla temizlendi");
            }
            catch (Exception ex)
            {
                return new ErrorResult($"Cache temizlenirken hata oluştu: {ex.Message}");
            }
        }

        [SecuredOperation("owner")]
        public IResult ClearTenant(int tenantId)
        {
            try
            {
                _cacheManager.ClearTenant(tenantId);
                return new SuccessResult($"Tenant {tenantId} cache'i başarıyla temizlendi");
            }
            catch (Exception ex)
            {
                return new ErrorResult($"Tenant cache temizlenirken hata oluştu: {ex.Message}");
            }
        }

        [SecuredOperation("owner")]
        public IResult ClearEntity(int tenantId, string entityName)
        {
            try
            {
                _cacheManager.RemoveByEntity(tenantId, entityName);
                return new SuccessResult($"Tenant {tenantId} - {entityName} entity cache'i başarıyla temizlendi");
            }
            catch (Exception ex)
            {
                return new ErrorResult($"Entity cache temizlenirken hata oluştu: {ex.Message}");
            }
        }

        [SecuredOperation("owner")]
        public IResult TestCache()
        {
            try
            {
                var isHealthy = _cacheManager.IsHealthy();
                var message = isHealthy ? "Cache sistemi çalışıyor" : "Cache sisteminde sorun var";

                if (isHealthy)
                    return new SuccessResult(message);
                else
                    return new ErrorResult(message);
            }
            catch (Exception ex)
            {
                return new ErrorResult($"Cache test edilirken hata oluştu: {ex.Message}");
            }
        }

        [SecuredOperation("owner")]
        public IDataResult<CacheStatistics> GetTenantStatistics(int tenantId)
        {
            try
            {
                var statistics = _cacheManager.GetStatistics();
                return new SuccessDataResult<CacheStatistics>(statistics, $"Tenant {tenantId} cache istatistikleri başarıyla getirildi");
            }
            catch (Exception ex)
            {
                return new ErrorDataResult<CacheStatistics>($"Tenant cache istatistikleri alınırken hata oluştu: {ex.Message}");
            }
        }

        [SecuredOperation("owner")]
        public IDataResult<Dictionary<string, object>> GetDetailedStatistics()
        {
            try
            {
                var performanceMetrics = _cacheManager.GetPerformanceMetrics();
                var tenantSizes = _cacheManager.GetAllTenantSizes();
                var activeTenants = _cacheManager.GetActiveTenants();

                var detailedStats = new Dictionary<string, object>
                {
                    ["PerformanceMetrics"] = performanceMetrics,
                    ["TenantSizes"] = tenantSizes,
                    ["ActiveTenants"] = activeTenants,
                    ["TotalTenants"] = activeTenants.Count
                };

                return new SuccessDataResult<Dictionary<string, object>>(detailedStats, "Detaylı cache istatistikleri başarıyla getirildi");
            }
            catch (Exception ex)
            {
                return new ErrorDataResult<Dictionary<string, object>>($"Detaylı cache istatistikleri alınırken hata oluştu: {ex.Message}");
            }
        }

        [SecuredOperation("owner")]
        public IDataResult<string[]> GetTenantKeys(int tenantId)
        {
            try
            {
                var pattern = $"T{tenantId}:*";
                var keys = _cacheManager.GetKeysByPattern(pattern);
                return new SuccessDataResult<string[]>(keys, $"Tenant {tenantId} cache anahtarları başarıyla getirildi");
            }
            catch (Exception ex)
            {
                return new ErrorDataResult<string[]>($"Tenant cache anahtarları alınırken hata oluştu: {ex.Message}");
            }
        }

        [SecuredOperation("owner")]
        public IDataResult<string[]> GetEntityKeys(int tenantId, string entityName)
        {
            try
            {
                var pattern = $"T{tenantId}:E_{entityName}:*";
                var keys = _cacheManager.GetKeysByPattern(pattern);
                return new SuccessDataResult<string[]>(keys, $"Tenant {tenantId} - {entityName} entity cache anahtarları başarıyla getirildi");
            }
            catch (Exception ex)
            {
                return new ErrorDataResult<string[]>($"Entity cache anahtarları alınırken hata oluştu: {ex.Message}");
            }
        }

        [SecuredOperation("owner")]
        public IDataResult<long> GetTenantCacheSize(int tenantId)
        {
            try
            {
                var size = _cacheManager.GetTenantCacheSize(tenantId);
                return new SuccessDataResult<long>(size, $"Tenant {tenantId} cache boyutu başarıyla getirildi");
            }
            catch (Exception ex)
            {
                return new ErrorDataResult<long>($"Tenant cache boyutu alınırken hata oluştu: {ex.Message}");
            }
        }

        [SecuredOperation("owner")]
        public IDataResult<Dictionary<int, long>> GetAllTenantSizes()
        {
            try
            {
                var sizes = _cacheManager.GetAllTenantSizes();
                return new SuccessDataResult<Dictionary<int, long>>(sizes, "Tüm tenant cache boyutları başarıyla getirildi");
            }
            catch (Exception ex)
            {
                return new ErrorDataResult<Dictionary<int, long>>($"Tenant cache boyutları alınırken hata oluştu: {ex.Message}");
            }
        }

        [SecuredOperation("owner")]
        public IDataResult<List<int>> GetActiveTenants()
        {
            try
            {
                var tenants = _cacheManager.GetActiveTenants();
                return new SuccessDataResult<List<int>>(tenants, $"Aktif {tenants.Count} tenant bulundu");
            }
            catch (Exception ex)
            {
                return new ErrorDataResult<List<int>>($"Aktif tenant'lar alınırken hata oluştu: {ex.Message}");
            }
        }

        [SecuredOperation("owner")]
        public IDataResult<Dictionary<string, int>> GetEntityCounts(int tenantId)
        {
            try
            {
                var counts = _cacheManager.GetEntityCounts(tenantId);
                return new SuccessDataResult<Dictionary<string, int>>(counts, $"Tenant {tenantId} entity sayıları başarıyla getirildi");
            }
            catch (Exception ex)
            {
                return new ErrorDataResult<Dictionary<string, int>>($"Entity sayıları alınırken hata oluştu: {ex.Message}");
            }
        }

        [SecuredOperation("owner")]
        public IDataResult<List<CacheItemDetail>> GetCacheDetails(int tenantId)
        {
            try
            {
                var details = _cacheManager.GetCacheDetails(tenantId);
                return new SuccessDataResult<List<CacheItemDetail>>(details, $"Tenant {tenantId} cache detayları başarıyla getirildi");
            }
            catch (Exception ex)
            {
                return new ErrorDataResult<List<CacheItemDetail>>($"Cache detayları alınırken hata oluştu: {ex.Message}");
            }
        }

        [SecuredOperation("owner")]
        public IDataResult<CacheItemDetail> GetCacheItemDetail(string key)
        {
            try
            {
                var detail = _cacheManager.GetCacheItemDetail(key);
                if (detail != null)
                    return new SuccessDataResult<CacheItemDetail>(detail, "Cache item detayı başarıyla getirildi");
                else
                    return new ErrorDataResult<CacheItemDetail>("Cache item bulunamadı");
            }
            catch (Exception ex)
            {
                return new ErrorDataResult<CacheItemDetail>($"Cache item detayı alınırken hata oluştu: {ex.Message}");
            }
        }

        [SecuredOperation("owner")]
        public IDataResult<Dictionary<string, object>> GetPerformanceMetrics()
        {
            try
            {
                var metrics = _cacheManager.GetPerformanceMetrics();
                return new SuccessDataResult<Dictionary<string, object>>(metrics, "Performans metrikleri başarıyla getirildi");
            }
            catch (Exception ex)
            {
                return new ErrorDataResult<Dictionary<string, object>>($"Performans metrikleri alınırken hata oluştu: {ex.Message}");
            }
        }
    }
}
