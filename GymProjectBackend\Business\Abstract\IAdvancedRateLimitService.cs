using Core.Utilities.Results;

namespace Business.Abstract
{
    public interface IAdvancedRateLimitService
    {
        // Login Rate Limiting
        IResult CheckLoginAttempt(string ipAddress, string deviceFingerprint);
        IResult RecordFailedLogin(string ipAddress, string deviceFingerprint);
        IResult RecordSuccessfulLogin(string ipAddress, string deviceFingerprint);
        IDataResult<int> GetRemainingLoginBanTime(string ipAddress, string deviceFingerprint);

        // Register Rate Limiting
        IResult CheckRegisterAttempt(string ipAddress);
        IResult RecordSuccessfulRegister(string ipAddress);
        IDataResult<int> GetRemainingRegisterBanTime(string ipAddress);

        // Profile Image Upload Rate Limiting
        IResult CheckProfileImageUploadAttempt(int userId);
        IResult RecordProfileImageUpload(int userId);
        IDataResult<int> GetRemainingProfileImageUploads(int userId);

        // File Download Rate Limiting
        IResult CheckFileDownloadAttempt(int userId);
        IResult RecordFileDownload(int userId);
        IDataResult<int> GetRemainingFileDownloads(int userId);

        // Device Fingerprinting
        string GenerateDeviceFingerprint(string ipAddress, string userAgent, string deviceInfo);
    }
}
