﻿using Castle.DynamicProxy;
using Core.Utilities.Interceptors;
using System;
using System.Transactions;

namespace Core.Aspects.Autofac.Transaction
{
    public class TransactionScopeAspect : MethodInterception
    {
        private readonly TransactionScopeOption _option;
        private readonly IsolationLevel _isolationLevel;
        private readonly TimeSpan _scopeTimeout;

        public TransactionScopeAspect(
            TransactionScopeOption option = TransactionScopeOption.Required,
            IsolationLevel isolationLevel = IsolationLevel.ReadCommitted,
            int timeoutInSeconds = 60)
        {
            _option = option;
            _isolationLevel = isolationLevel;
            _scopeTimeout = TimeSpan.FromSeconds(timeoutInSeconds);
        }

        public override void Intercept(IInvocation invocation)
        {
            using (var transactionScope = new TransactionScope(_option,
                new TransactionOptions { IsolationLevel = _isolationLevel, Timeout = _scopeTimeout }))
            {
                try
                {
                    invocation.Proceed();
                    transactionScope.Complete();
                }
                catch (Exception)
                {
                    // Transaction otomatik olarak rollback olacak
                    throw;
                }
            }
        }
    }
}