/* <PERSON><PERSON><PERSON><PERSON> */
.stats-container {
    display: flex;
    justify-content: space-around;
    flex-wrap: wrap;
    gap: 2rem;
    margin: 3rem 0;
}

.stat-box {
    flex: 1;
    min-width: 200px;
    max-width: 300px;
    background-color: var(--bg-dark-tertiary);
    border-radius: var(--border-radius);
    padding: 2rem;
    text-align: center;
    box-shadow: var(--card-shadow-dark);
    border: 1px solid var(--border-color-dark);
    transition: all 0.3s ease;
}

.stat-box:hover {
    transform: translateY(-10px);
    box-shadow: var(--card-shadow-dark-hover);
    border-color: var(--primary-color);
}

.stat-icon {
    font-size: 2.5rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
    display: flex;
    justify-content: center;
}

.stat-number {
    font-size: 3rem;
    font-weight: 700;
    color: var(--light-color);
    margin-bottom: 0.5rem;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.stat-box p {
    font-size: 1.1rem;
    color: var(--text-color-dark);
}

.benefits-container {
    display: flex;
    flex-wrap: wrap;
    gap: 2rem;
    margin: 3rem 0;
}

.benefit-card {
    flex: 1;
    min-width: 300px;
    background-color: var(--bg-dark-tertiary);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--card-shadow-dark);
    border: 1px solid var(--border-color-dark);
    transition: all 0.3s ease;
    padding: 2.5rem 2rem;
    text-align: center;
}

.benefit-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--card-shadow-dark-hover);
    border-color: var(--primary-color);
}

.benefit-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background-color: rgba(67, 97, 238, 0.1);
    color: var(--primary-color);
    font-size: 2rem;
    margin: 0 auto 1.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.benefit-card:hover .benefit-icon {
    background-color: var(--primary-color);
    color: var(--light-color);
    transform: scale(1.1);
}

.benefit-card h3 {
    font-size: 1.3rem;
    margin-bottom: 1rem;
    color: var(--light-color);
}

.benefit-card p {
    font-size: 1rem;
    line-height: 1.7;
    color: var(--text-color-dark-secondary);
}

.testimonial-note {
    text-align: center;
    margin-top: 3rem;
    padding: 2rem;
    background-color: var(--bg-dark-tertiary);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color-dark);
}

.testimonial-note p {
    font-size: 1.1rem;
    margin-bottom: 1.5rem;
    color: var(--text-color-dark);
}

/* Responsive Tasarım */
@media (max-width: 992px) {
    .stats-container {
        gap: 1.5rem;
    }

    .benefits-container {
        flex-direction: column;
        align-items: center;
    }

    .benefit-card {
        max-width: 450px;
        width: 100%;
    }
}

@media (max-width: 768px) {
    .stat-box {
        min-width: 100%;
    }

    .stat-number {
        font-size: 2.5rem;
    }

    .testimonial-text {
        font-size: 1rem;
    }
}
