import { createRequire } from 'module';const require = createRequire(import.meta.url);
import {
  Mat<PERSON><PERSON><PERSON>,
  MatFormField,
  MatHint,
  Mat<PERSON><PERSON>l,
  MatPrefix,
  MatSuffix
} from "./chunk-A5ZXGX3N.js";
import {
  MatCommonModule,
  ObserversModule
} from "./chunk-V6OU2FZJ.js";
import {
  NgModule,
  setClassMetadata,
  ɵɵdefineInjector,
  ɵɵdefineNgModule
} from "./chunk-NNB67BKT.js";
import {
  require_operators
} from "./chunk-HGVHWTGE.js";
import {
  require_cjs
} from "./chunk-EXQLYBKH.js";
import {
  __toESM
} from "./chunk-GBTWTWDP.js";

// node_modules/@angular/cdk/fesm2022/observers.mjs
var import_rxjs = __toESM(require_cjs(), 1);
var import_operators = __toESM(require_operators(), 1);

// node_modules/@angular/material/fesm2022/module-3bb03da5.mjs
var MatFormFieldModule = class _MatFormFieldModule {
  static ɵfac = function MatFormFieldModule_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _MatFormFieldModule)();
  };
  static ɵmod = ɵɵdefineNgModule({
    type: _MatFormFieldModule,
    imports: [MatCommonModule, ObserversModule, MatFormField, MatLabel, MatError, MatHint, MatPrefix, MatSuffix],
    exports: [MatFormField, MatLabel, MatHint, MatError, MatPrefix, MatSuffix, MatCommonModule]
  });
  static ɵinj = ɵɵdefineInjector({
    imports: [MatCommonModule, ObserversModule, MatCommonModule]
  });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(MatFormFieldModule, [{
    type: NgModule,
    args: [{
      imports: [MatCommonModule, ObserversModule, MatFormField, MatLabel, MatError, MatHint, MatPrefix, MatSuffix],
      exports: [MatFormField, MatLabel, MatHint, MatError, MatPrefix, MatSuffix, MatCommonModule]
    }]
  }], null, null);
})();

export {
  MatFormFieldModule
};
//# sourceMappingURL=chunk-K76TAQ7S.js.map
