using Core.CrossCuttingConcerns.Caching.Models;
using Core.Utilities.Results;
using System.Collections.Generic;

namespace Business.Abstract
{
    public interface ICacheService
    {
        // Cache istatistikleri
        IDataResult<CacheStatistics> GetStatistics();
        IDataResult<CacheStatistics> GetTenantStatistics(int tenantId);
        IDataResult<Dictionary<string, object>> GetDetailedStatistics();

        // Cache sağlık durumu
        IDataResult<Dictionary<string, object>> GetHealthInfo();

        // Cache anahtarları
        IDataResult<string[]> GetAllKeys();
        IDataResult<string[]> GetKeysByPattern(string pattern);
        IDataResult<string[]> GetTenantKeys(int tenantId);
        IDataResult<string[]> GetEntityKeys(int tenantId, string entityName);

        // Cache boyutu
        IDataResult<long> GetCacheSize();
        IDataResult<long> GetTenantCacheSize(int tenantId);
        IDataResult<Dictionary<int, long>> GetAllTenantSizes();

        // Cache temizleme
        IResult ClearAll();
        IResult ClearTenant(int tenantId);
        IResult ClearEntity(int tenantId, string entityName);

        // Cache test
        IResult TestCache();

        // Tenant yönetimi
        IDataResult<List<int>> GetActiveTenants();
        IDataResult<Dictionary<string, int>> GetEntityCounts(int tenantId);

        // Cache detayları
        IDataResult<List<CacheItemDetail>> GetCacheDetails(int tenantId);
        IDataResult<CacheItemDetail> GetCacheItemDetail(string key);

        // Performans
        IDataResult<Dictionary<string, object>> GetPerformanceMetrics();
    }
}
