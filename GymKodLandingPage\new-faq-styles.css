/* Yeni SSS Bölümü <PERSON> */
.new-faq-container {
    max-width: 800px;
    margin: 3rem auto;
}

.new-faq-item {
    background-color: var(--bg-dark-tertiary);
    border-radius: var(--border-radius);
    margin-bottom: 1rem;
    overflow: hidden;
    box-shadow: var(--card-shadow-dark);
    border: 1px solid var(--border-color-dark);
    transition: all 0.3s ease;
}

.new-faq-item:hover {
    border-color: var(--border-color-dark-light);
    transform: translateY(-3px);
    box-shadow: var(--card-shadow-dark-hover);
}

.new-faq-question {
    padding: 1.5rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: space-between;
    transition: all 0.3s ease;
}

.new-faq-question h3 {
    font-size: 1.1rem;
    margin: 0;
    color: var(--text-color-dark);
    font-weight: 500;
    transition: color 0.3s ease;
    flex: 1;
}

.new-faq-icon {
    color: var(--primary-color);
    min-width: 24px;
    text-align: center;
    margin-left: 1rem;
}

.new-faq-icon .fa-minus {
    display: none;
}

.new-faq-item.active .new-faq-icon .fa-plus {
    display: none;
}

.new-faq-item.active .new-faq-icon .fa-minus {
    display: inline-block;
}

.new-faq-item.active .new-faq-question h3 {
    color: var(--primary-color);
}

.new-faq-answer {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.4s ease-in-out, padding 0.4s ease-in-out;
    padding: 0 1.5rem;
    opacity: 0;
}

.new-faq-item.active .new-faq-answer {
    max-height: 500px;
    padding: 0 1.5rem 1.5rem;
    opacity: 1;
}

.new-faq-answer p {
    margin: 0;
    color: var(--text-color-dark-secondary);
    line-height: 1.7;
}

/* Mobil Uyumluluk */
@media (max-width: 768px) {
    .new-faq-question {
        padding: 1.2rem;
    }
    
    .new-faq-question h3 {
        font-size: 1rem;
    }
    
    .new-faq-item.active .new-faq-answer {
        padding: 0 1.2rem 1.2rem;
    }
}
