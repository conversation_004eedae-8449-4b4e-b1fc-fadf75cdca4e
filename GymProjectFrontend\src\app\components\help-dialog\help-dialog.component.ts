// src/app/components/help-dialog/help-dialog.component.ts
import { Component, Inject } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { HelpDialogData, HelpContent } from '../../models/help-guide.model';

@Component({
  selector: 'app-help-dialog',
  standalone: false,
  templateUrl: './help-dialog.component.html',
  styleUrls: ['./help-dialog.component.css']
})
export class HelpDialogComponent {
  
  constructor(
    public dialogRef: MatDialogRef<HelpDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: HelpDialogData
  ) {}

  onCloseClick(): void {
    this.dialogRef.close();
  }

  getContentTypeIcon(content: HelpContent): string {
    switch (content.type) {
      case 'steps':
        return 'fas fa-list-ol';
      case 'list':
        return 'fas fa-list-ul';
      case 'warning':
        return content.icon || 'fas fa-exclamation-triangle';
      case 'tip':
        return content.icon || 'fas fa-lightbulb';
      case 'text':
      default:
        return content.icon || 'fas fa-info-circle';
    }
  }

  getContentTypeClass(content: HelpContent): string {
    switch (content.type) {
      case 'warning':
        return 'content-warning';
      case 'tip':
        return 'content-tip';
      case 'steps':
        return 'content-steps';
      case 'list':
        return 'content-list';
      case 'text':
      default:
        return 'content-text';
    }
  }

  isArray(content: string | string[]): content is string[] {
    return Array.isArray(content);
  }
}
