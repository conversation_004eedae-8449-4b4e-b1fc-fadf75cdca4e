using System;

namespace Core.CrossCuttingConcerns.Caching.Models
{
    public class CacheItemDetail
    {
        public string Key { get; set; }
        public int TenantId { get; set; }
        public string EntityName { get; set; }
        public string[] Tags { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime ExpiresAt { get; set; }
        public TimeSpan TimeToLive => ExpiresAt - DateTime.UtcNow;
        public bool IsExpired => DateTime.UtcNow > ExpiresAt;
        public long SizeInBytes { get; set; }
        public string ValueType { get; set; }
        public int AccessCount { get; set; }
        public DateTime LastAccessedAt { get; set; }
        
        // Hesaplanan özellikler
        public string FormattedSize => FormatBytes(SizeInBytes);
        public string FormattedTTL => FormatTimeSpan(TimeToLive);
        public string Status => IsExpired ? "Expired" : "Active";
        
        private string FormatBytes(long bytes)
        {
            if (bytes == 0) return "0 B";
            string[] sizes = { "B", "KB", "MB", "GB" };
            int order = 0;
            double size = bytes;
            while (size >= 1024 && order < sizes.Length - 1)
            {
                order++;
                size = size / 1024;
            }
            return $"{size:0.##} {sizes[order]}";
        }
        
        private string FormatTimeSpan(TimeSpan timeSpan)
        {
            if (timeSpan.TotalDays >= 1)
                return $"{timeSpan.Days}d {timeSpan.Hours}h";
            if (timeSpan.TotalHours >= 1)
                return $"{timeSpan.Hours}h {timeSpan.Minutes}m";
            if (timeSpan.TotalMinutes >= 1)
                return $"{timeSpan.Minutes}m {timeSpan.Seconds}s";
            return $"{timeSpan.Seconds}s";
        }
    }
}
