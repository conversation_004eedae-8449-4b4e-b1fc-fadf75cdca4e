import { createRequire } from 'module';const require = createRequire(import.meta.url);
import "./chunk-GBTWTWDP.js";

// node_modules/@angular/common/locales/tr.mjs
var u = void 0;
function plural(val) {
  const n = val;
  if (n === 1) return 1;
  return 5;
}
var tr_default = ["tr", [["öö", "ös"], ["ÖÖ", "ÖS"], u], [["ÖÖ", "ÖS"], u, u], [["P", "P", "S", "Ç", "P", "C", "C"], ["Paz", "Pzt", "Sal", "Çar", "Per", "Cum", "Cmt"], ["Pazar", "Pazartesi", "Sal<PERSON>", "Çarşamba", "Perşembe", "Cuma", "Cumartesi"], ["Pa", "Pt", "Sa", "Ça", "<PERSON>e", "<PERSON><PERSON>", "Ct"]], u, [["O", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "H", "T", "A", "E", "E", "<PERSON>", "A"], ["<PERSON>ca", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "May", "<PERSON>z", "Tem", "Ağu", "<PERSON>yl", "Eki", "Kas", "Ara"], ["<PERSON>cak", "Şubat", "Mart", "Nisan", "Mayıs", "Haziran", "Temmuz", "<PERSON>ğustos", "<PERSON><PERSON>ül", "<PERSON>kim", "<PERSON>sım", "Aralık"]], u, [["MÖ", "MS"], u, ["Milattan Önce", "Milattan Sonra"]], 1, [6, 0], ["d.MM.y", "d MMM y", "d MMMM y", "d MMMM y EEEE"], ["HH:mm", "HH:mm:ss", "HH:mm:ss z", "HH:mm:ss zzzz"], ["{1} {0}", u, u, u], [",", ".", ";", "%", "+", "-", "E", "×", "‰", "∞", "NaN", ":"], ["#,##0.###", "%#,##0", "¤#,##0.00", "#E0"], "TRY", "₺", "Türk Lirası", {
  "AUD": ["AU$", "$"],
  "BYN": [u, "р."],
  "PHP": [u, "₱"],
  "RON": [u, "L"],
  "RUR": [u, "р."],
  "THB": ["฿"],
  "TRY": ["₺"],
  "TWD": ["NT$"]
}, "ltr", plural];
export {
  tr_default as default
};
/*! Bundled license information:

@angular/common/locales/tr.mjs:
  (**
   * @license
   * Copyright Google LLC All Rights Reserved.
   *
   * Use of this source code is governed by an MIT-style license that can be
   * found in the LICENSE file at https://angular.dev/license
   *)
*/
//# sourceMappingURL=@angular_common_locales_tr.js.map
