/* <PERSON><PERSON> Stiller ve Resetleme */
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap');

/* Dark Mode Renk Paleti */
:root {
    --primary-color: #4361ee; /* <PERSON><PERSON>en mavi renk */
    --primary-color-light: rgba(67, 97, 238, 0.1); /* Hafif mavi ton */
    --primary-color-dark: #3a56d4; /* Daha koyu mavi ton */
    --secondary-color: #3f37c9; /* Daha koyu mavi ton */
    --secondary-color-light: rgba(63, 55, 201, 0.1); /* Hafif koyu mavi ton */
    --accent-color: #4cc9f0; /* Açık mavi vurgu rengi */
    --accent-color-light: rgba(76, 201, 240, 0.1); /* Hafif açık mavi ton */
    --success-color: #2ecc71; /* <PERSON><PERSON><PERSON> renk */
    --warning-color: #f39c12; /* <PERSON><PERSON><PERSON> renk */
    --danger-color: #e74c3c; /* <PERSON><PERSON><PERSON><PERSON>z<PERSON> renk */

    --bg-dark: #121212; /* Ana Koyu Arka Plan */
    --bg-dark-secondary: #1E1E1E; /* İkincil Koyu Arka Plan (Kartlar vb.) */
    --bg-dark-tertiary: #2a2a2a; /* Daha açık koyu ton (Hover vb.) */
    --bg-dark-quaternary: #333333; /* En açık koyu ton */
    --text-color-dark: #E0E0E0; /* Ana Açık Metin */
    --text-color-dark-secondary: #A0A0A0; /* İkincil Açık Metin (Daha soluk) */
    --border-color-dark: #333333; /* Koyu Kenarlık Rengi */
    --border-color-dark-light: #444444; /* Daha açık koyu kenarlık rengi */

    --light-color: #ffffff; /* Açık renkleri hala kullanabiliriz */
    --header-height: 70px;
    --section-padding: 80px 0; /* Bölüm padding'i artırıldı */
    --card-shadow-dark: 0 8px 20px rgba(0, 0, 0, 0.3); /* Daha belirgin gölge */
    --card-shadow-dark-hover: 0 15px 30px rgba(0, 0, 0, 0.5); /* Hover durumunda daha belirgin gölge */
    --border-radius: 8px; /* Biraz daha yumuşak köşeler */
    --border-radius-lg: 12px; /* Daha büyük yumuşak köşeler */
    --border-radius-xl: 20px; /* Çok büyük yumuşak köşeler */
    --border-radius-pill: 50px; /* Pill şeklinde köşeler */

    /* Tam sayfa bölümleri için değişkenler */
    --section-height: auto;
    --transition-speed: 0.3s;
    --transition-speed-slow: 0.5s;
    --transition-speed-fast: 0.2s;
    --transition-timing: cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    overflow-x: hidden;
}

#page-sections-container {
    height: auto;
    overflow-y: visible;
    /* Kaydırma efekti kaldırıldı */
}

body {
    font-family: 'Poppins', sans-serif;
    background-color: var(--bg-dark);
    color: var(--text-color-dark);
    line-height: 1.7;
    overflow-x: hidden;
    font-size: 16px;
}

h1, h2, h3, h4 {
    line-height: 1.3;
    margin-bottom: 1rem;
    font-weight: 600;
    color: var(--light-color); /* Başlıklar açık renk */
}

h1 { font-size: 3rem; margin-bottom: 1.5rem; }
h2 { font-size: 2.5rem; margin-bottom: 0.5rem; } /* Section başlıkları */
h3 { font-size: 1.3rem; }
h4 { font-size: 1.1rem; font-weight: 600; }


p {
    margin-bottom: 1rem;
    color: var(--text-color-dark-secondary); /* İkincil metin rengi */
}

a {
    text-decoration: none;
    color: var(--primary-color); /* Linkler ana renk */
    transition: color 0.3s ease;
}
a:hover {
    color: var(--secondary-color); /* Hover'da ikincil renk */
}

ul {
    list-style: none;
}

img {
    max-width: 100%;
    height: auto;
    display: block;
}

.container {
    max-width: 1140px;
    margin: 0 auto;
    padding: 0 15px;
}

.highlight {
    color: var(--accent-color); /* Vurgu rengi */
    font-weight: 600;
}

/* Bölümler için Stiller */
.section {
    min-height: 100vh; /* Minimum yükseklik */
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: visible; /* Normal kaydırmaya izin ver */
    padding: 80px 0;
    box-sizing: border-box; /* Padding'i yüksekliğe dahil et */
}

.section-content {
    width: 100%;
    max-width: 1140px;
    margin: 0 auto;
    padding: 0 15px;
    z-index: 2;
}

/* Kaydırma Göstergeleri */
.pagination {
    position: fixed;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    flex-direction: column;
    z-index: 1000;
    pointer-events: auto; /* Tıklamaya izin ver */
}

.pagination-bullet {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.3);
    margin: 8px 0;
    cursor: pointer;
    transition: all 0.3s ease;
}

.pagination-bullet.active {
    background-color: var(--primary-color);
    transform: scale(1.3);
}

.pagination-bullet:hover {
    background-color: var(--accent-color);
}

/* Genel Bölüm Stilleri */
.section-padding {
    padding: var(--section-padding);
}

/* Header yüksekliği kadar padding ekle */
.intro-section {
    padding-top: calc(var(--header-height) + 50px) !important;
}

.section-title {
    text-align: center;
    margin-bottom: 0.8rem;
    font-size: 2rem;
    font-weight: 700;
    color: var(--light-color); /* Açık renk başlık */
}

.section-subtitle {
    text-align: center;
    max-width: 650px; /* Biraz daha geniş */
    margin: 0 auto 3.5rem auto; /* Alt boşluk artırıldı */
    color: var(--text-color-dark-secondary);
    font-size: 1.1rem;
}

.bg-light { /* Bu sınıf artık ikincil koyu arka planı temsil edecek */
    background-color: var(--bg-dark-secondary);
}

/* Header ve Navbar */
.header {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: var(--header-height);
    background-color: var(--bg-dark-secondary); /* Header arka planı */
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2); /* Koyu temaya uygun gölge */
    z-index: 1000;
    transition: background-color 0.3s ease, box-shadow 0.3s ease;
    scroll-snap-align: none; /* Header'a snap uygulanmasın */
}
.header.scrolled {
     background-color: rgba(30, 30, 30, 0.95); /* Kaydırma sırasında biraz saydam */
     box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}


.navbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 100%;
}

.logo {
    font-size: 1.9rem;
    font-weight: 700;
    color: var(--secondary-color); /* Logo ikincil renk */
}
.logo:hover {
    color: var(--secondary-color);
}

.nav-links {
    display: flex;
    align-items: center;
}

.nav-links li {
    margin-left: 30px;
}

.nav-links a {
    color: var(--text-color-dark); /* Nav linkleri açık renk */
    font-weight: 500;
    padding-bottom: 5px;
    position: relative;
}
.nav-links a::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background-color: var(--secondary-color); /* Alt çizgi ikincil renk */
    transition: width 0.3s ease;
}
.nav-links a:hover::after {
    width: 100%;
}
.nav-links a:hover {
    color: var(--text-color-dark);
}


.cta-button {
    display: inline-block;
    background-color: var(--primary-color); /* CTA ana renk */
    color: var(--bg-dark) !important; /* Koyu metin */
    padding: 16px 36px;
    border-radius: var(--border-radius-pill);
    font-weight: 700;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    font-size: 1.1rem;
    position: relative;
    overflow: hidden;
    box-shadow: 0 10px 25px rgba(67, 97, 238, 0.4);
    text-transform: uppercase;
    letter-spacing: 0.8px;
    z-index: 1;
    text-align: center;
}

.cta-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, var(--primary-color), var(--accent-color));
    z-index: -1;
    transition: opacity 0.3s ease;
    opacity: 0;
}

.cta-button:hover {
    background-color: transparent;
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(67, 97, 238, 0.6);
    color: var(--bg-dark) !important;
}

.cta-button:hover::before {
    opacity: 1;
}

.cta-button:hover::after {
    width: 0;
}

.cta-button:active {
    transform: translateY(-2px);
    box-shadow: 0 8px 15px rgba(67, 97, 238, 0.4);
}

.large-cta {
    padding: 18px 40px;
    font-size: 1.2rem;
    font-weight: 800;
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    border: 2px solid transparent;
    position: relative;
    overflow: hidden;
}

.large-cta::after {
    content: '';
    position: absolute;
    width: 150%;
    height: 150%;
    background: linear-gradient(45deg, var(--accent-color), var(--primary-color));
    top: -25%;
    left: -25%;
    transform: translateX(-100%) rotate(45deg);
    transition: transform 0.6s ease;
    z-index: -1;
}

.large-cta:hover::after {
    transform: translateX(0) rotate(45deg);
}

/* Animasyonlar */
.pulse-animation {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(67, 97, 238, 0.7);
    }
    70% {
        box-shadow: 0 0 0 15px rgba(67, 97, 238, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(67, 97, 238, 0);
    }
}

/* Yeni animasyonlar */
.fade-in-up {
    animation: fadeInUp 1s ease forwards;
}

.fade-in-down {
    animation: fadeInDown 1s ease forwards;
}

.fade-in-left {
    animation: fadeInLeft 1s ease forwards;
}

.fade-in-right {
    animation: fadeInRight 1s ease forwards;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Hover animasyonları */
.hover-lift {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
}

.hover-scale {
    transition: transform 0.3s ease;
}

.hover-scale:hover {
    transform: scale(1.05);
}


.nav-cta {
    margin-left: 20px;
    padding: 10px 25px;
    font-size: 0.9rem;
    border-radius: var(--border-radius);
    text-transform: none;
    letter-spacing: normal;
    box-shadow: 0 5px 15px rgba(67, 97, 238, 0.2);
}

.menu-toggle {
    display: none;
    background: none;
    border: none;
    font-size: 1.6rem;
    color: var(--secondary-color); /* Mobil menü ikonu ikincil renk */
    cursor: pointer;
}

/* 1. Hero Bölümü */
.hero-section {
    /* Koyu gradient arka plan - Daha etkileyici bir görsel */
    background: linear-gradient(rgba(18, 18, 18, 0.8), rgba(18, 18, 18, 0.9)), url('https://images.unsplash.com/photo-1534438327276-14e5300c3a48?q=80&w=1200&h=800&auto=format') no-repeat center center/cover;
    height: 100vh; /* Tam ekran yükseklik */
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--light-color);
    padding-top: var(--header-height);
    position: relative;
    background-attachment: fixed; /* Parallax etkisi */
    overflow: hidden;
}

/* Hero Particles */
.hero-particles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

/* Hero Wave */
.hero-wave {
    position: absolute;
    bottom: -1px;
    left: 0;
    width: 100%;
    z-index: 5;
    line-height: 0;
}

.hero-wave svg {
    width: 100%;
    height: auto;
}

/* Hero Content */
.hero-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;
    z-index: 2;
    text-align: left;
    gap: 3rem;
    flex-wrap: wrap; /* Mobil görünümde alt alta gelsin */
}

@media (max-width: 992px) {
    .hero-content {
        flex-direction: column;
        text-align: center;
    }

    .hero-buttons {
        justify-content: center;
    }
}

.hero-text {
    flex: 1;
    max-width: 600px;
}

.hero-image {
    flex: 1;
    max-width: 500px;
    position: relative;
    display: block; /* Her zaman göster */
    margin: 0 auto; /* Ortalama */
    transition: all 0.5s ease;
}

@media (max-width: 992px) {
    .hero-image {
        max-width: 400px;
        margin-top: 2rem;
    }
}

@media (max-width: 768px) {
    .hero-image {
        max-width: 320px;
    }
}

.dashboard-preview {
    width: 100%;
    height: auto;
    border-radius: var(--border-radius-lg);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4), 0 0 0 1px var(--border-color-dark-light), 0 0 30px rgba(67, 97, 238, 0.2);
    border: 1px solid var(--border-color-dark-light);
    transition: all var(--transition-speed) var(--transition-timing);
    transform: perspective(1000px) rotateY(-5deg) rotateX(5deg);
    object-fit: cover;
    max-height: 400px;
    max-width: 100%;
}

.dashboard-preview:hover {
    transform: perspective(1000px) rotateY(0deg) rotateX(0deg) translateY(-10px) scale(1.02);
    box-shadow: 0 30px 60px rgba(0, 0, 0, 0.5), 0 0 0 1px var(--primary-color), 0 0 30px rgba(67, 97, 238, 0.4);
}

/* Floating Badges */
.floating-badge {
    position: absolute;
    background-color: var(--bg-dark-tertiary);
    color: var(--text-color-dark);
    padding: 0.8rem 1.2rem;
    border-radius: var(--border-radius-pill);
    font-size: 0.9rem;
    font-weight: 500;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
    border: 1px solid var(--border-color-dark-light);
    display: flex;
    align-items: center;
    gap: 0.5rem;
    z-index: 2;
}

.floating-badge i {
    color: var(--accent-color);
    font-size: 1.1rem;
}

.top-right {
    top: -15px;
    right: -15px;
    animation: float 3s ease-in-out infinite;
}

.bottom-left {
    bottom: -15px;
    left: -15px;
    animation: float 4s ease-in-out infinite;
}

.bottom-right {
    bottom: -15px;
    right: -15px;
    animation: float 3.5s ease-in-out infinite;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-10px);
    }
}

.hero-content h1 {
    font-size: 3.5rem; /* Başlık daha da büyütüldü */
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: var(--light-color);
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3); /* Metin gölgesi eklendi */
    letter-spacing: -0.5px; /* Harfler arası mesafe azaltıldı */
    line-height: 1.2;
}

@media (min-width: 768px) {
    .hero-content h1 {
        font-size: 4rem;
    }
}

.hero-content h1 .highlight {
    position: relative;
    display: inline-block;
}

.hero-content h1 .highlight::after {
    content: '';
    position: absolute;
    bottom: 5px;
    left: 0;
    width: 100%;
    height: 8px;
    background-color: rgba(76, 201, 240, 0.3);
    z-index: -1;
    border-radius: 4px;
}

.hero-content h2 {
    font-size: 2rem; /* Alt başlık büyütüldü */
    font-weight: 400;
    margin-bottom: 1.5rem;
    color: var(--text-color-dark); /* Ana açık metin rengi */
    text-shadow: 0 2px 8px rgba(0, 0, 0, 0.2); /* Metin gölgesi eklendi */
}

@media (min-width: 768px) {
    .hero-content h2 {
        font-size: 2.4rem;
    }
}

.hero-content p {
    font-size: 1.1rem; /* Paragraf büyütüldü */
    margin-bottom: 2rem;
    color: var(--text-color-dark-secondary);
    line-height: 1.8;
}

@media (min-width: 768px) {
    .hero-content p {
        font-size: 1.3rem;
    }
}

.hero-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    align-items: center;
}

.text-button {
    color: var(--accent-color);
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 0;
    transition: all var(--transition-speed) var(--transition-timing);
    font-size: 1rem;
}

.text-button i {
    transition: transform var(--transition-speed) var(--transition-timing);
}

.text-button:hover {
    color: var(--light-color);
}

.text-button:hover i {
    transform: translateX(5px);
}

.large-cta {
    padding: 16px 40px; /* Buton daha da büyütüldü */
    font-size: 1.1rem;
    position: relative;
    overflow: hidden;
    z-index: 1;
    letter-spacing: 0.5px;
    font-weight: 700;
    text-transform: uppercase;
    border-radius: var(--border-radius);
}

@media (min-width: 768px) {
    .large-cta {
        padding: 18px 50px;
        font-size: 1.2rem;
    }
}

.large-cta::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    z-index: -1;
    transition: opacity 0.3s ease;
    opacity: 0;
}

.large-cta:hover::before {
    opacity: 1;
}

.large-cta:hover {
    box-shadow: 0 10px 25px rgba(67, 97, 238, 0.4);
    transform: translateY(-3px);
}

/* Pulse Animation */
.pulse-animation {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(67, 97, 238, 0.7);
    }
    70% {
        box-shadow: 0 0 0 15px rgba(67, 97, 238, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(67, 97, 238, 0);
    }
}

/* 2. Giriş (Intro) Bölümü */
.intro-section {
    background-color: var(--bg-dark-secondary); /* İkincil koyu arka plan */
    position: relative;
}

/* İstatistik Bölümü */
.intro-stats {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 2rem;
    margin: 2rem 0 3rem;
    padding: 1rem;
    background-color: var(--bg-dark-tertiary);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--card-shadow-dark);
}

.stat-item {
    flex: 1;
    min-width: 200px;
    text-align: center;
    padding: 1.5rem;
    border-radius: var(--border-radius);
    transition: all var(--transition-speed) var(--transition-timing);
}

.stat-item:hover {
    background-color: var(--bg-dark-quaternary);
    transform: translateY(-5px);
}

.stat-number {
    font-size: 3rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
    line-height: 1;
}

.stat-item p {
    color: var(--text-color-dark);
    font-size: 1.1rem;
    font-weight: 500;
    margin: 0;
}

/* Counter Animation */
.counter {
    display: inline-block;
}

.intro-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2.5rem; /* Aralık artırıldı */
    margin-top: 3.5rem;
}

.intro-item, .solution {
    text-align: center;
    padding: 1.5rem; /* İç boşluk */
}

.intro-item .icon, .solution .icon {
    font-size: 3.2rem;
    margin-bottom: 1.2rem;
    color: var(--primary-color);
}
.solution .icon {
    color: var(--secondary-color);
}

.intro-item h3, .solution h3 {
    font-size: 1.5rem; /* Başlıklar büyütüldü */
    margin-bottom: 0.8rem;
    color: var(--light-color); /* Açık renk başlık */
}

.intro-item p, .solution p {
    font-size: 1rem;
    color: var(--text-color-dark-secondary);
}


/* Intro Card Stilleri */
.intro-card {
    background-color: var(--bg-dark-tertiary); /* Daha açık koyu ton */
    padding: 2.5rem;
    border-radius: var(--border-radius-lg);
    text-align: center;
    box-shadow: var(--card-shadow-dark);
    border: 1px solid var(--border-color-dark);
    transition: transform var(--transition-speed) var(--transition-timing),
                box-shadow var(--transition-speed) var(--transition-timing),
                border-color var(--transition-speed) var(--transition-timing);
    position: relative;
    overflow: hidden;
}

.intro-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(67, 97, 238, 0.05) 0%, rgba(76, 201, 240, 0.05) 100%);
    opacity: 0;
    transition: opacity var(--transition-speed) var(--transition-timing);
}

.intro-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--card-shadow-dark-hover);
    border-color: var(--primary-color);
}

.intro-card:hover::before {
    opacity: 1;
}

.intro-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background-color: rgba(67, 97, 238, 0.1);
    color: var(--primary-color);
    font-size: 2.2rem;
    margin: 0 auto 2rem auto;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all var(--transition-speed) var(--transition-timing);
    position: relative;
    z-index: 2;
    box-shadow: 0 5px 15px rgba(67, 97, 238, 0.2);
    border: 2px solid transparent;
}

.intro-card:hover .intro-icon {
    background-color: var(--primary-color);
    color: var(--light-color);
    transform: scale(1.1);
    border-color: var(--accent-color);
    box-shadow: 0 8px 25px rgba(67, 97, 238, 0.4);
}

.intro-card h3 {
    font-size: 1.4rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--light-color);
    position: relative;
    z-index: 2;
}

.intro-card p {
    font-size: 1rem;
    color: var(--text-color-dark-secondary);
    line-height: 1.6;
    position: relative;
    z-index: 2;
}

/* 3. Ana Özellikler Bölümü - Yeniden Tasarlandı */
.features-section {
     background-color: var(--bg-dark); /* Ana koyu arka plan */
     position: relative;
     overflow: auto; /* Özellikler bölümünde kaydırmaya izin ver */
}

.features-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at 10% 20%, rgba(67, 97, 238, 0.03) 0%, transparent 80%);
    pointer-events: none;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); /* Kart genişliği arttırıldı */
    gap: 2.5rem; /* Aralık arttırıldı */
    margin-top: 3rem; /* Üst boşluk arttırıldı */
    padding-bottom: 2rem; /* Alt boşluk eklendi */
}

.feature-card {
    background-color: var(--bg-dark-secondary); /* Kart arka planı */
    padding: 2.5rem 2rem; /* İç boşluk arttırıldı */
    border-radius: 16px; /* Daha yumuşak köşeler */
    text-align: center;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.25); /* Daha belirgin gölge */
    border: 1px solid var(--border-color-dark); /* Kenarlık eklendi */
    transition: transform 0.4s ease, box-shadow 0.4s ease, border-color 0.4s ease;
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(5px);
    margin-bottom: 1rem; /* Kartlar arası mesafe */
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 100%;
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(67, 97, 238, 0.08) 0%, rgba(76, 201, 240, 0.08) 100%);
    opacity: 0;
    transition: opacity 0.4s ease;
}

.feature-card::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(67, 97, 238, 0.1) 0%, transparent 70%);
    opacity: 0;
    transform: scale(0.5);
    transition: transform 0.6s ease, opacity 0.6s ease;
}

.feature-card:hover {
    transform: translateY(-15px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.35);
    border-color: var(--primary-color); /* Hover'da kenarlık rengi */
}

.feature-card:hover::before {
    opacity: 1;
}

.feature-card:hover::after {
    opacity: 0.8;
    transform: scale(1);
}

.card-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background-color: rgba(67, 97, 238, 0.12); /* Ana rengin hafif tonu - daha belirgin */
    color: var(--primary-color);
    font-size: 2.2rem; /* İkon boyutu arttırıldı */
    margin: 0 auto 2rem auto; /* Alt boşluk arttırıldı */
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275); /* Daha canlı animasyon */
    position: relative;
    z-index: 2;
    box-shadow: 0 8px 20px rgba(67, 97, 238, 0.25);
    border: 2px solid transparent;
}

.feature-card:hover .card-icon {
    background-color: var(--primary-color);
    color: var(--light-color); /* Hover'da ikon rengi beyaz */
    transform: scale(1.15) rotate(5deg);
    border-color: var(--accent-color);
    box-shadow: 0 12px 30px rgba(67, 97, 238, 0.5);
}

.feature-card h3 {
    font-size: 1.4rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--light-color);
    position: relative;
    z-index: 2;
    transition: color 0.3s ease;
}

.feature-card:hover h3 {
    color: var(--accent-color);
}

.feature-card p {
    font-size: 1rem;
    color: var(--text-color-dark-secondary);
    line-height: 1.6;
    position: relative;
    z-index: 2;
    margin-bottom: 1.5rem;
}

.feature-link {
    display: inline-flex;
    align-items: center;
    color: var(--primary-color);
    font-weight: 600;
    font-size: 0.95rem;
    margin-top: auto;
    position: relative;
    z-index: 2;
    transition: all 0.3s ease;
}

.feature-link i {
    margin-left: 8px;
    font-size: 0.85rem;
    transition: transform 0.3s ease;
}

.feature-link:hover {
    color: var(--accent-color);
}

.feature-link:hover i {
    transform: translateX(5px);
}

.feature-item { display: none; }


/* 4. Faydalar Bölümü */
.benefits-section {
     background-color: var(--bg-dark-secondary); /* İkincil koyu arka plan */
}

.benefits-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr)); /* Daha geniş kartlar */
    gap: 2.5rem; /* Aralık artırıldı */
    margin-top: 3.5rem;
}

.benefit-item {
    display: flex;
    align-items: center;
    background-color: transparent; /* Arka plan kaldırıldı */
    padding: 1rem 0; /* Sadece dikey padding */
    border-radius: 0; /* Radius kaldırıldı */
    border-bottom: 1px solid var(--border-color-dark); /* Alt çizgi */
    transition: border-color 0.3s ease;
}
.benefit-item:hover {
    border-color: var(--secondary-color); /* Hover'da çizgi rengi */
    background-color: transparent; /* Hover arka planı yok */
}
/* Son elemanın alt çizgisini kaldır */
.benefit-item:last-child {
    border-bottom: none;
}


.benefit-item .icon {
    font-size: 2.8rem; /* İkon büyütüldü */
    color: var(--secondary-color);
    margin-right: 1.8rem; /* Aralık artırıldı */
    min-width: 50px; /* Genişlik artırıldı */
    text-align: center;
}

.benefit-item div {
    text-align: left;
}

.benefit-item h3 {
    font-size: 1.25rem; /* Başlık büyütüldü */
    margin-bottom: 0.4rem;
    color: var(--light-color);
}

.benefit-item p {
    color: var(--text-color-dark-secondary);
    font-size: 1rem; /* Metin büyütüldü */
    margin-bottom: 0;
}

/* 5. Son CTA Bölümü */
.contact-section {
    background-color: var(--bg-dark-tertiary); /* Daha açık koyu ton */
    color: var(--light-color);
    text-align: center;
    display: flex;
    flex-direction: column;
    justify-content: center;
    min-height: auto !important; /* Yükseklik sınırlamasını kaldır */
    padding-bottom: 100px; /* Alt boşluğu artır */
}

.contact-section .section-title,
.contact-section .section-subtitle {
    color: var(--light-color);
}
.contact-section .highlight {
    color: var(--secondary-color);
}

.contact-section .section-subtitle {
    color: var(--text-color-dark); /* Ana açık metin */
    margin-bottom: 2.5rem;
}

.cta-container {
    margin: 2rem 0 3rem;
    display: flex;
    justify-content: center;
    align-items: center;
}

.contact-section .large-cta {
    margin-bottom: 1.5rem;
    background-color: var(--primary-color); /* Ana renk buton */
    color: var(--bg-dark) !important; /* Koyu metin */
    padding: 20px 60px; /* Daha büyük buton */
    font-size: 1.3rem; /* Daha büyük yazı */
    box-shadow: 0 10px 30px rgba(67, 97, 238, 0.3); /* Belirgin gölge */
}

.contact-section .large-cta:hover {
    background-color: var(--secondary-color); /* Hover'da ikincil renk */
    color: var(--bg-dark) !important;
    transform: translateY(-5px); /* Daha belirgin hover efekti */
    box-shadow: 0 15px 35px rgba(67, 97, 238, 0.5); /* Daha belirgin gölge */
}

.contact-details {
    margin-top: 1.5rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.contact-details p {
    color: var(--text-color-dark-secondary);
    font-size: 1rem;
    margin-bottom: 0.8rem;
}

.guarantee-text {
    display: inline-block;
    margin: 2rem auto 1rem;
    padding: 1.2rem 2rem;
    background-color: rgba(67, 97, 238, 0.15);
    border-radius: var(--border-radius);
    border-left: 4px solid var(--primary-color);
    font-weight: 500;
    font-size: 1.1rem;
    max-width: 600px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.guarantee-text:hover {
    background-color: rgba(67, 97, 238, 0.2);
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.guarantee-text i {
    color: var(--primary-color);
    margin-right: 0.8rem;
    font-size: 1.3rem;
}

.contact-details a {
    color: var(--primary-color); /* Link ana renk */
    font-weight: 600;
    text-decoration: none; /* Alt çizgi yok */
}
.contact-details a:hover {
    color: var(--secondary-color);
    text-decoration: underline; /* Hover'da alt çizgi */
}


/* Footer */
.footer {
    background-color: var(--bg-dark-secondary); /* İkincil koyu */
    color: var(--text-color-dark-secondary);
    padding: 60px 0 0 0; /* Üst padding artırıldı */
    text-align: left;
    border-top: 1px solid var(--border-color-dark); /* Üst kenarlık */
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2.5rem; /* Aralık artırıldı */
    padding-bottom: 40px; /* Boşluk artırıldı */
    border-bottom: 1px solid var(--border-color-dark); /* Ayırıcı çizgi rengi */
    margin-bottom: 25px; /* Boşluk artırıldı */
}

.footer h3, .footer h4 {
    color: var(--light-color);
    margin-bottom: 1.2rem; /* Boşluk artırıldı */
}
.footer h3 { font-size: 1.6rem; color: var(--secondary-color); } /* Logo rengi */
.footer h4 { font-size: 1.15rem; }

.footer p {
    color: var(--text-color-dark-secondary);
    font-size: 0.95rem;
    line-height: 1.7;
}

.footer-links ul li {
    margin-bottom: 0.9rem; /* Aralık artırıldı */
}

.footer-links a {
    color: var(--text-color-dark-secondary);
    transition: color 0.3s ease, padding-left 0.3s ease;
}

.footer-links a:hover {
    color: var(--secondary-color); /* Hover ikincil renk */
    padding-left: 5px;
}

.footer-contact p {
    margin-bottom: 0.9rem; /* Aralık artırıldı */
    display: flex;
    align-items: center;
}
.footer-contact i {
    margin-right: 12px; /* Aralık artırıldı */
    color: var(--primary-color); /* İkon ana renk */
    width: 20px;
    text-align: center;
}

.social-icons {
    margin-top: 1.2rem; /* Boşluk artırıldı */
}

.social-icons a {
    color: var(--text-color-dark-secondary);
    font-size: 1.3rem; /* İkonlar büyütüldü */
    margin-right: 18px; /* Aralık artırıldı */
    transition: color 0.3s ease, transform 0.3s ease;
    display: inline-block;
}

.social-icons a:hover {
    color: var(--primary-color); /* Hover ana renk */
    transform: translateY(-3px);
}

.footer-bottom {
    text-align: center;
    padding: 25px 0; /* Boşluk artırıldı */
}

.footer-bottom p {
    font-size: 0.9rem;
    color: var(--text-color-dark-secondary);
    margin: 0;
}


/* Yeni Özellikler ve Bölümler için CSS */
@import url('style-features.css');

/* Duyarlılık Ayarları (Dark Mode için Gözden Geçirildi) */

@media (max-width: 992px) {
    h1 { font-size: 2.8rem; }
    .hero-content h1 { font-size: 3rem; } /* Mobil hero başlığı */
    h2 { font-size: 2.2rem; }
    .section-title { font-size: 2.1rem; }
    .features-grid { grid-template-columns: repeat(auto-fit, minmax(240px, 1fr)); }
    .benefits-grid { grid-template-columns: 1fr; } /* Faydalar tek sütun */
    .intro-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    .intro-card {
        padding: 2rem;
        max-width: 400px;
        margin: 0 auto;
    }
    .intro-icon {
        width: 70px;
        height: 70px;
        margin-bottom: 1.5rem;
    }
    .footer-content { grid-template-columns: repeat(auto-fit, minmax(220px, 1fr)); }

    /* Kaydırma göstergeleri */
    .pagination {
        right: 10px;
    }

    .pagination-bullet {
        width: 10px;
        height: 10px;
        margin: 6px 0;
    }
}

@media (max-width: 768px) {
    :root { --header-height: 65px; }
    .navbar { padding: 0 15px; }
    .nav-links {
        /* Mobil menü arka planı */
        position: fixed;
        top: var(--header-height);
        left: 0;
        width: 100%;
        flex-direction: column;
        background-color: var(--bg-dark-secondary);
        box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        padding: 20px 0;
        display: none;
        z-index: 999;
    }
    .nav-links a { color: var(--text-color-dark); } /* Mobil link rengi */
    .nav-links.active { display: flex; }
    .nav-links li { margin: 15px 0; }
    .menu-toggle { display: block; color: var(--accent-color); }
    .nav-cta { display: none; }

    .section-padding { padding: 50px 0; } /* Mobil bölüm padding'i */
    .section { padding: 60px 0; }

    h1 { font-size: 2.4rem; }
    .hero-content h1 { font-size: 2.6rem; }
    .hero-content h2 { font-size: 1.6rem; }
    h2 { font-size: 2rem; }
    .section-title { font-size: 1.9rem; }
    h3 { font-size: 1.25rem; }

    .section { height: auto; min-height: 100vh; }
    .hero-section { height: 100vh; }
    .hero-content p { font-size: 1.1rem; }

    .features-grid {
        grid-template-columns: repeat(2, 1fr); /* 2 sütun */
        gap: 1.5rem;
    }
    .feature-card {
        padding: 1.5rem;
        max-width: 100%;
        margin: 0 auto;
    }
    .card-icon {
        width: 60px;
        height: 60px;
        margin-bottom: 1rem;
        font-size: 1.7rem;
    }

    .benefit-item { padding: 1.5rem 0; } /* Dikey padding */

    .footer-content { grid-template-columns: 1fr; text-align: center; }
    .footer-about, .footer-links, .footer-contact { margin-bottom: 2rem; }
    .social-icons { text-align: center; }
    .footer-contact p { justify-content: center; }

    /* Kaydırma göstergeleri */
    .pagination {
        right: 5px;
    }

    .pagination-bullet {
        width: 8px;
        height: 8px;
        margin: 5px 0;
    }
}

@media (max-width: 576px) {
    :root { --header-height: 60px; }
    .logo { font-size: 1.7rem; }
    h1 { font-size: 2.1rem; }
    .hero-content h1 { font-size: 2.3rem; }
    .hero-content h2 { font-size: 1.4rem; }
    h2 { font-size: 1.8rem; }
    .section-title { font-size: 1.7rem; }
    .section-subtitle { font-size: 1rem; margin-bottom: 2.5rem;}
    .hero-content p { font-size: 1rem; }
    .cta-button { padding: 12px 28px; font-size: 1rem; }
    .large-cta { padding: 14px 35px; font-size: 1.05rem; }
    .features-grid {
        grid-template-columns: 1fr; /* Tek sütun */
    }
    .feature-card {
        padding: 1.5rem;
        max-width: 280px;
    }
    .card-icon {
        width: 50px;
        height: 50px;
        font-size: 1.5rem;
        margin-bottom: 1rem;
    }
    .feature-card h3 {
        font-size: 1.1rem;
    }
    .feature-card p {
        font-size: 0.85rem;
    }
    .benefit-item { padding: 1.2rem 0; }
    .section-padding { padding: 40px 0; }
    .section { padding: 40px 0; }

    /* Kaydırma göstergeleri mobil için */
    .pagination {
        right: 3px;
    }

    .pagination-bullet {
        width: 6px;
        height: 6px;
        margin: 4px 0;
    }

    /* Kaydırma animasyonu */
    .hero-section::after {
        width: 20px;
        height: 35px;
        bottom: 20px;
    }

    .hero-section::before {
        width: 6px;
        height: 6px;
        bottom: 40px;
    }
}

/* İletişim Formu Stilleri */
.contact-form-container {
    max-width: 800px;
    margin: 0 auto;
    background-color: var(--bg-dark-tertiary);
    border-radius: var(--border-radius-lg);
    padding: 2.5rem;
    box-shadow: var(--card-shadow-dark);
    border: 1px solid var(--border-color-dark);
}

.contact-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.form-row {
    display: flex;
    gap: 1.5rem;
    width: 100%;
}

.form-group {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.form-group label {
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--light-color);
    font-size: 0.95rem;
}

.form-group input,
.form-group textarea {
    padding: 1rem 1.2rem;
    border-radius: var(--border-radius);
    background-color: var(--bg-dark-quaternary);
    border: 1px solid var(--border-color-dark-light);
    color: var(--light-color);
    font-family: 'Poppins', sans-serif;
    font-size: 1rem;
    transition: all 0.3s ease;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(67, 97, 238, 0.2), inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.form-group input:hover,
.form-group textarea:hover {
    border-color: var(--border-color-dark-light);
    background-color: rgba(42, 42, 42, 1);
}

.form-group textarea {
    min-height: 120px;
    resize: vertical;
}

.form-submit {
    margin-top: 1rem;
    display: flex;
    justify-content: center;
}

@media (max-width: 768px) {
    .form-row {
        flex-direction: column;
        gap: 1rem;
    }

    .contact-form-container {
        padding: 1.5rem;
    }
}
