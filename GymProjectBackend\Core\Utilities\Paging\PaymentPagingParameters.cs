﻿namespace Core.Utilities.Paging
{
    // Core/Utilities/Paging/PaymentPagingParameters.cs
    public class PaymentPagingParameters : PagingParameters
    {
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public string? PaymentMethod { get; set; }
        public bool? IsActive { get; set; }

        public PaymentPagingParameters()
        {
            PageSize = 20; // Varsayılan sayfa boyutu
            PageNumber = 1; // Varsayılan sayfa numarası
            SearchText = ""; // Boş arama metni
        }
    }
}
