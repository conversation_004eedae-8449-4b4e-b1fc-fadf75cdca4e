﻿using Core.Utilities.Results;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Business.Abstract
{
    public interface ICompanyAdressService
    {
        IDataResult<List<CompanyAdress>> GetAll();
        IResult Add(CompanyAdress companyAdress);
        IResult Update(CompanyAdress companyAdress);
        IResult Delete(int id);
        IDataResult<List<CompanyAdressDetailDto>> GetCompanyAdressDetails();

    }
}
