﻿using System.Linq;

namespace Core.Utilities.Paging
{
    // Core/Utilities/Paging/MemberPagingParameters.cs
    public class MemberPagingParameters : PagingParameters
    {
        public int? Gender { get; set; }
        public string Branch { get; set; } = ""; // Default boş string
        public bool? IsActive { get; set; }
        public string SearchText { get; set; } = ""; // Default boş string
        public int[] MembershipTypeIds { get; set; } = new int[0]; // Çoklu paket filtreleme için

        /// <summary>
        /// Cache key generation için consistent string representation
        /// </summary>
        public override string ToString()
        {
            var membershipTypeIdsStr = MembershipTypeIds?.Length > 0
                ? string.Join(",", MembershipTypeIds.OrderBy(x => x))
                : "";

            return $"PageNumber:{PageNumber}|PageSize:{PageSize}|SearchText:{SearchText ?? ""}|SortBy:{SortBy ?? ""}|SortDirection:{SortDirection ?? ""}|Gender:{Gender?.ToString() ?? "null"}|Branch:{Branch ?? ""}|IsActive:{IsActive?.ToString() ?? "null"}|MembershipTypeIds:{membershipTypeIdsStr}";
        }
    }
}
