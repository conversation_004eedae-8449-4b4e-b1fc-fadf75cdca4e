using Core.Entities;
using System;
using System.Collections.Generic;

namespace Entities.DTOs
{
    public class MonthlyExpenseDto : IDto
    {
        /// <summary>
        /// Yıl bilgisi
        /// </summary>
        public int Year { get; set; }

        /// <summary>
        /// Ay isimleri (Türkçe)
        /// </summary>
        public List<string> Months { get; set; } = new List<string>();

        /// <summary>
        /// Aylık gider tutarları (12 ay)
        /// </summary>
        public List<decimal> MonthlyExpense { get; set; } = new List<decimal>();
    }
}
