-- Antrenman Programı Sistemi Migration Script
-- Bu script antrenman programı şablonu sistemini oluşturur
-- WorkoutProgramTemplate: Ana program şablonları (salon bazlı)
-- WorkoutProgramDay: Program günleri
-- WorkoutProgramExercise: Gün bazlı egzersizler

USE [GymProject]
GO

-- 1. WorkoutProgramTemplate Tablosu (Ana program şablonları)
CREATE TABLE [dbo].[WorkoutProgramTemplates](
    [WorkoutProgramTemplateID] [int] IDENTITY(1,1) NOT NULL,
    [CompanyID] [int] NOT NULL,
    [ProgramName] [nvarchar](200) NOT NULL,
    [Description] [nvarchar](1000) NULL,
    [ExperienceLevel] [nvarchar](50) NULL, -- Başlangıç, Orta, İleri
    [TargetGoal] [nvarchar](100) NULL, -- <PERSON>lo Alma, <PERSON>lo Verme, <PERSON><PERSON>
    [IsActive] [bit] NULL DEFAULT(1),
    [CreationDate] [datetime2](7) NULL DEFAULT(GETDATE()),
    [DeletedDate] [datetime2](7) NULL,
    [UpdatedDate] [datetime2](7) NULL,
    CONSTRAINT [PK_WorkoutProgramTemplates] PRIMARY KEY CLUSTERED ([WorkoutProgramTemplateID] ASC),
    CONSTRAINT [FK_WorkoutProgramTemplates_Companies] FOREIGN KEY([CompanyID]) 
        REFERENCES [dbo].[Companies] ([CompanyID])
)
GO

-- 2. WorkoutProgramDays Tablosu (Program günleri)
CREATE TABLE [dbo].[WorkoutProgramDays](
    [WorkoutProgramDayID] [int] IDENTITY(1,1) NOT NULL,
    [WorkoutProgramTemplateID] [int] NOT NULL,
    [CompanyID] [int] NOT NULL, -- ✅ Güvenlik için eklendi
    [DayNumber] [int] NOT NULL, -- 1, 2, 3, 4, 5, 6, 7
    [DayName] [nvarchar](100) NOT NULL, -- Göğüs-Triceps, Sırt-Biceps, Dinlenme vb.
    [IsRestDay] [bit] NOT NULL DEFAULT(0),
    [CreationDate] [datetime2](7) NULL DEFAULT(GETDATE()),
    CONSTRAINT [PK_WorkoutProgramDays] PRIMARY KEY CLUSTERED ([WorkoutProgramDayID] ASC),
    CONSTRAINT [FK_WorkoutProgramDays_WorkoutProgramTemplates] FOREIGN KEY([WorkoutProgramTemplateID])
        REFERENCES [dbo].[WorkoutProgramTemplates] ([WorkoutProgramTemplateID]) ON DELETE CASCADE,
    CONSTRAINT [FK_WorkoutProgramDays_Companies] FOREIGN KEY([CompanyID])
        REFERENCES [dbo].[Companies] ([CompanyID]) -- ✅ Güvenlik için eklendi
)
GO

-- 3. WorkoutProgramExercises Tablosu (Gün bazlı egzersizler)
CREATE TABLE [dbo].[WorkoutProgramExercises](
    [WorkoutProgramExerciseID] [int] IDENTITY(1,1) NOT NULL,
    [WorkoutProgramDayID] [int] NOT NULL,
    [CompanyID] [int] NOT NULL, -- ✅ Güvenlik için eklendi
    [ExerciseType] [nvarchar](20) NOT NULL, -- "System" veya "Company"
    [ExerciseID] [int] NOT NULL, -- SystemExerciseID veya CompanyExerciseID
    [OrderIndex] [int] NOT NULL, -- Egzersiz sırası (1, 2, 3...)
    [Sets] [int] NOT NULL, -- Set sayısı
    [Reps] [nvarchar](50) NOT NULL, -- Tekrar sayısı (12, MAX, 12-15 vb.)
    [RestTime] [int] NULL, -- Dinlenme süresi (saniye)
    [Notes] [nvarchar](500) NULL, -- Egzersiz notları
    [CreationDate] [datetime2](7) NULL DEFAULT(GETDATE()),
    CONSTRAINT [PK_WorkoutProgramExercises] PRIMARY KEY CLUSTERED ([WorkoutProgramExerciseID] ASC),
    CONSTRAINT [FK_WorkoutProgramExercises_WorkoutProgramDays] FOREIGN KEY([WorkoutProgramDayID])
        REFERENCES [dbo].[WorkoutProgramDays] ([WorkoutProgramDayID]) ON DELETE CASCADE,
    CONSTRAINT [FK_WorkoutProgramExercises_Companies] FOREIGN KEY([CompanyID])
        REFERENCES [dbo].[Companies] ([CompanyID]) -- ✅ Güvenlik için eklendi
)
GO

-- 4. PERFORMANS İNDEXLERİ (10.000+ kullanıcı için optimizasyon)

-- WorkoutProgramTemplates için indexler
CREATE NONCLUSTERED INDEX [IX_WorkoutProgramTemplates_CompanyID_IsActive] 
ON [dbo].[WorkoutProgramTemplates] ([CompanyID], [IsActive])
INCLUDE ([ProgramName], [CreationDate])
GO

CREATE NONCLUSTERED INDEX [IX_WorkoutProgramTemplates_ProgramName] 
ON [dbo].[WorkoutProgramTemplates] ([ProgramName])
WHERE [IsActive] = 1
GO

-- WorkoutProgramDays için güvenli indexler (CompanyID dahil)
CREATE NONCLUSTERED INDEX [IX_WorkoutProgramDays_CompanyID_TemplateID]
ON [dbo].[WorkoutProgramDays] ([CompanyID], [WorkoutProgramTemplateID])
INCLUDE ([DayNumber], [DayName], [IsRestDay])
GO

CREATE NONCLUSTERED INDEX [IX_WorkoutProgramDays_TemplateID_DayNumber]
ON [dbo].[WorkoutProgramDays] ([WorkoutProgramTemplateID], [DayNumber])
GO

-- WorkoutProgramExercises için güvenli indexler (CompanyID dahil)
CREATE NONCLUSTERED INDEX [IX_WorkoutProgramExercises_CompanyID_DayID_OrderIndex]
ON [dbo].[WorkoutProgramExercises] ([CompanyID], [WorkoutProgramDayID], [OrderIndex])
INCLUDE ([ExerciseType], [ExerciseID], [Sets], [Reps])
GO

CREATE NONCLUSTERED INDEX [IX_WorkoutProgramExercises_ExerciseType_ExerciseID]
ON [dbo].[WorkoutProgramExercises] ([ExerciseType], [ExerciseID])
GO

-- 5. KAPSAM KONTROLÜ (Constraint'ler)

-- ExerciseType sadece "System" veya "Company" olabilir
ALTER TABLE [dbo].[WorkoutProgramExercises]
ADD CONSTRAINT [CK_WorkoutProgramExercises_ExerciseType] 
CHECK ([ExerciseType] IN ('System', 'Company'))
GO

-- DayNumber 1-7 arasında olmalı
ALTER TABLE [dbo].[WorkoutProgramDays]
ADD CONSTRAINT [CK_WorkoutProgramDays_DayNumber] 
CHECK ([DayNumber] >= 1 AND [DayNumber] <= 7)
GO

-- Sets pozitif olmalı
ALTER TABLE [dbo].[WorkoutProgramExercises]
ADD CONSTRAINT [CK_WorkoutProgramExercises_Sets] 
CHECK ([Sets] > 0)
GO

-- OrderIndex pozitif olmalı
ALTER TABLE [dbo].[WorkoutProgramExercises]
ADD CONSTRAINT [CK_WorkoutProgramExercises_OrderIndex] 
CHECK ([OrderIndex] > 0)
GO

-- 6. UNIQUE CONSTRAINT'LER

-- Aynı şirkette aynı isimde program olamaz (aktif olanlar için)
CREATE UNIQUE NONCLUSTERED INDEX [IX_WorkoutProgramTemplates_CompanyID_ProgramName_Unique] 
ON [dbo].[WorkoutProgramTemplates] ([CompanyID], [ProgramName])
WHERE [IsActive] = 1
GO

-- Aynı programda aynı gün numarası olamaz
CREATE UNIQUE NONCLUSTERED INDEX [IX_WorkoutProgramDays_TemplateID_DayNumber_Unique] 
ON [dbo].[WorkoutProgramDays] ([WorkoutProgramTemplateID], [DayNumber])
GO

-- Aynı günde aynı sırada egzersiz olamaz
CREATE UNIQUE NONCLUSTERED INDEX [IX_WorkoutProgramExercises_DayID_OrderIndex_Unique] 
ON [dbo].[WorkoutProgramExercises] ([WorkoutProgramDayID], [OrderIndex])
GO

PRINT 'Antrenman Programı Sistemi migration tamamlandı!'
PRINT 'Oluşturulan tablolar:'
PRINT '- WorkoutProgramTemplates (Ana program şablonları)'
PRINT '- WorkoutProgramDays (Program günleri)'
PRINT '- WorkoutProgramExercises (Gün bazlı egzersizler)'
PRINT 'Performans indexleri ve constraint''ler eklendi.'
GO
