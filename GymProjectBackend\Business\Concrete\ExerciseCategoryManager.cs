using Business.Abstract;
using Business.BusinessAscpects.Autofac;
using Core.Aspects.Autofac.Logging;
using Core.Aspects.Autofac.Performance;
using Core.Aspects.Autofac.Transaction;
using Core.Utilities.Results;
using DataAccess.Abstract;
using Entities.DTOs;

namespace Business.Concrete
{
    public class ExerciseCategoryManager : IExerciseCategoryService
    {
        readonly IExerciseCategoryDal _exerciseCategoryDal;

        public ExerciseCategoryManager(IExerciseCategoryDal exerciseCategoryDal)
        {
            _exerciseCategoryDal = exerciseCategoryDal;
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        public IDataResult<List<ExerciseCategoryDto>> GetAllCategories()
        {
            var result = _exerciseCategoryDal.GetAllCategories();
            return new SuccessDataResult<List<ExerciseCategoryDto>>(result);
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        public IDataResult<List<ExerciseCategoryDto>> GetActiveCategories()
        {
            var result = _exerciseCategoryDal.GetActiveCategories();
            return new SuccessDataResult<List<ExerciseCategoryDto>>(result);
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        public IDataResult<ExerciseCategoryDto> GetById(int categoryId)
        {
            var result = _exerciseCategoryDal.GetCategoryById(categoryId);
            if (result == null)
            {
                return new ErrorDataResult<ExerciseCategoryDto>("Egzersiz kategorisi bulunamadı.");
            }

            return new SuccessDataResult<ExerciseCategoryDto>(result);
        }

        [SecuredOperation("owner")] // Sadece owner kategorileri ekleyebilir
        [LogAspect]
        [TransactionScopeAspect]
        [PerformanceAspect(3)]
        public IResult Add(ExerciseCategoryAddDto categoryAddDto)
        {
            return _exerciseCategoryDal.AddExerciseCategory(categoryAddDto);
        }

        [SecuredOperation("owner")] // Sadece owner kategorileri güncelleyebilir
        [LogAspect]
        [TransactionScopeAspect]
        [PerformanceAspect(3)]
        public IResult Update(ExerciseCategoryUpdateDto categoryUpdateDto)
        {
            return _exerciseCategoryDal.UpdateExerciseCategory(categoryUpdateDto);
        }

        [SecuredOperation("owner")] // Sadece owner kategorileri silebilir
        [LogAspect]
        [TransactionScopeAspect]
        [PerformanceAspect(3)]
        public IResult Delete(int categoryId)
        {
            return _exerciseCategoryDal.DeleteExerciseCategory(categoryId);
        }
    }
}
