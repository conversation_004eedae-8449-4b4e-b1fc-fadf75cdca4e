﻿using Core.Utilities.Results;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Business.Abstract
{
    public  interface ICompanyService
    {
        IDataResult<List<Company>> GetAll();
        IDataResult<Company> GetById(int id);
        IResult Add(Company company);
        IResult Update(Company company);
        IResult Delete(int id);
        IDataResult<List<ActiveCompanyDetailDto>> GetActiveCompanies();

    }
}
