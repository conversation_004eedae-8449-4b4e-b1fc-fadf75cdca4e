using Core.Utilities.Results;
using Entities.Concrete;
using Entities.DTOs;
using System.Collections.Generic;

namespace Business.Abstract
{
    public interface IWorkoutProgramTemplateService
    {
        IDataResult<List<WorkoutProgramTemplateListDto>> GetAll();
        IDataResult<WorkoutProgramTemplateDto> GetById(int templateId);
        IResult Add(WorkoutProgramTemplateAddDto templateAddDto);
        IResult Update(WorkoutProgramTemplateUpdateDto templateUpdateDto);
        IResult Delete(int templateId);
    }
}
