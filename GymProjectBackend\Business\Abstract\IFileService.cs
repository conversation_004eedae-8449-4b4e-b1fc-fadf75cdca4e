using Core.Utilities.Results;
using Microsoft.AspNetCore.Http;
using System.Threading.Tasks;

namespace Business.Abstract
{
    public interface IFileService
    {
        // Async metodlar
        Task<IDataResult<string>> UploadProfileImageAsync(IFormFile file, int userId);

        // Backward compatibility için sync metodlar
        IDataResult<string> UploadProfileImage(IFormFile file, int userId);
        IResult DeleteProfileImage(int userId);
        IDataResult<string> GetProfileImagePath(int userId);
        IResult ValidateImageFile(IFormFile file);
    }
}
