/* Features Tabs - <PERSON><PERSON><PERSON><PERSON>, <PERSON>ler Alt Alta Gösterilecek */
.features-tabs {
    margin: 3rem 0;
}

/* .tabs-nav ve .tab-btn stilleri kaldırıldı */

.tabs-content {
    position: relative;
    /* min-height kaldırıldı, içerik belirley<PERSON>ek */
}

.tab-pane {
    display: block; /* Her zaman görünür */
    margin-bottom: 3rem; /* <PERSON>ler arasına b<PERSON> */
    /* animation: fadeIn 0.5s ease forwards; */ /* Animasyon kaldırıldı veya isteğe bağlı */
}

/* .tab-pane.active kuralı kaldırıldı */

.tab-content-inner {
    display: flex;
    align-items: stretch;
    gap: 3rem;
    background-color: var(--bg-dark-tertiary);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--card-shadow-dark);
    border: 1px solid var(--border-color-dark);
    position: relative;
}

.tab-content-inner::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(67, 97, 238, 0.05) 0%, rgba(76, 201, 240, 0.05) 100%);
    opacity: 0;
    transition: opacity 0.5s ease;
    pointer-events: none;
}

.tab-content-inner:hover::before {
    opacity: 1;
}

.tab-text {
    flex: 1;
    padding: 3.5rem;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.tab-image {
    flex: 1;
    position: relative;
    overflow: hidden;
    min-height: 300px;
    max-height: 350px;
    border-radius: 0 var(--border-radius-lg) var(--border-radius-lg) 0;
    box-shadow: inset 0 0 20px rgba(0, 0, 0, 0.2);
}

.tab-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.8s ease;
    position: absolute;
    top: 0;
    left: 0;
    border-radius: 0 var(--border-radius-lg) var(--border-radius-lg) 0;
    object-position: center;
}

.image-overlay-effect {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to right, var(--bg-dark-tertiary) 0%, transparent 20%);
    z-index: 2;
    pointer-events: none;
}

.tab-content-inner:hover .tab-image img {
    transform: scale(1.08);
}

.feature-list {
    margin: 1.8rem 0;
}

.feature-list li {
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    font-size: 1.05rem;
}

.feature-list li i {
    color: var(--success-color);
    margin-right: 12px;
    font-size: 1.1rem;
}

.feature-cta {
    display: inline-flex;
    align-items: center;
    gap: 10px;
    background-color: var(--primary-color);
    color: var(--light-color) !important;
    padding: 12px 25px;
    border-radius: var(--border-radius);
    font-weight: 500;
    margin-top: 1.5rem;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(67, 97, 238, 0.2);
}

.feature-cta i {
    transition: transform 0.3s ease;
}

.feature-cta:hover {
    background-color: var(--secondary-color);
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(67, 97, 238, 0.3);
}

.feature-cta:hover i {
    transform: translateX(5px);
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Mobil Uyumluluk */
@media (max-width: 992px) {
    .tab-content-inner {
        flex-direction: column;
    }

    .tab-image {
        min-height: 250px;
        max-height: 300px;
        order: -1; /* Görsel üstte */
        border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
    }

    .tab-image img {
        border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
        object-position: center;
    }

    .tab-text {
        padding: 2.5rem;
    }

    .image-overlay-effect {
        background: linear-gradient(to bottom, var(--bg-dark-tertiary) 0%, transparent 20%);
        border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
    }
}

@media (max-width: 768px) {
    .tabs-nav {
        flex-direction: column;
        align-items: center;
        gap: 1rem;
    }

    .tab-btn {
        width: 100%;
        max-width: 300px;
        justify-content: center;
        padding: 14px 20px;
        min-width: unset;
    }

    .tab-btn i {
        font-size: 1.2rem;
        width: 28px;
        height: 28px;
    }

    .tab-btn span {
        font-size: 1rem;
    }

    .tab-image {
        min-height: 200px;
        max-height: 250px;
    }

    .showcase-image {
        height: 250px;
        max-height: 250px;
    }
}

/* Testimonials Section */
.testimonials-section {
    background-color: var(--bg-dark);
    position: relative;
}

.testimonials-slider {
    position: relative;
    max-width: 800px;
    margin: 0 auto;
    overflow: hidden;
    padding: 2rem 0;
}

.testimonial-card {
    display: none;
    opacity: 0;
    transition: opacity var(--transition-speed) var(--transition-timing);
}

.testimonial-card.active {
    display: block;
    opacity: 1;
    animation: fadeIn 0.5s ease;
}

.testimonial-content {
    background-color: var(--bg-dark-tertiary);
    padding: 3rem;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--card-shadow-dark);
    border: 1px solid var(--border-color-dark);
    position: relative;
    transition: all 0.3s ease;
    transform: translateY(0);
}

.testimonial-content:hover {
    transform: translateY(-5px);
    box-shadow: var(--card-shadow-dark-hover);
    background-color: var(--bg-dark-quaternary);
}

.quote-icon {
    position: absolute;
    top: -15px;
    left: 30px;
    width: 40px;
    height: 40px;
    background-color: var(--primary-color);
    color: var(--bg-dark);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    box-shadow: 0 5px 15px rgba(67, 97, 238, 0.3);
}

.testimonial-text {
    font-size: 1.1rem;
    line-height: 1.8;
    color: var(--text-color-dark);
    margin-bottom: 2rem;
    font-style: italic;
}

.testimonial-author {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.author-image {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid var(--primary-color);
}

.author-info h4 {
    font-size: 1.1rem;
    margin-bottom: 0.2rem;
    color: var(--light-color);
}

.author-info p {
    font-size: 0.9rem;
    color: var(--text-color-dark-secondary);
    margin: 0;
}

.testimonial-controls {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 2rem;
    gap: 1rem;
}

.testimonial-btn {
    background: none;
    border: none;
    color: var(--text-color-dark);
    font-size: 1.2rem;
    cursor: pointer;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all var(--transition-speed) var(--transition-timing);
    background-color: var(--bg-dark-tertiary);
}

.testimonial-btn:hover {
    background-color: var(--primary-color);
    color: var(--bg-dark);
}

.testimonial-dots {
    display: flex;
    gap: 0.5rem;
}

.dot {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background-color: var(--bg-dark-tertiary);
    cursor: pointer;
    transition: all var(--transition-speed) var(--transition-timing);
}

.dot.active {
    background-color: var(--primary-color);
    transform: scale(1.3);
}

.client-logos {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
    gap: 2rem;
    margin-top: 4rem;
}

.logo-item {
    flex: 1;
    min-width: 150px;
    max-width: 200px;
    text-align: center;
    opacity: 0.7;
    transition: all var(--transition-speed) var(--transition-timing);
    background-color: var(--bg-dark-tertiary);
    padding: 1.5rem;
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color-dark);
    margin: 0.5rem;
    box-shadow: var(--card-shadow-dark);
}

.logo-item:hover {
    opacity: 1;
    transform: scale(1.05);
    box-shadow: var(--card-shadow-dark-hover);
    border-color: var(--primary-color);
}

.logo-item img {
    max-width: 100%;
    height: 60px;
    object-fit: contain;
    filter: grayscale(100%);
    transition: filter var(--transition-speed) var(--transition-timing);
}

.logo-item:hover img {
    filter: grayscale(0%);
}

/* Benefits Showcase */
.benefits-showcase {
    display: flex;
    flex-wrap: wrap;
    gap: 3rem;
    margin: 3rem 0;
    align-items: center;
}

.showcase-image {
    flex: 1;
    min-width: 300px;
    position: relative;
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--card-shadow-dark);
    height: 350px;
    max-height: 350px;
}

.showcase-image img {
    width: 100%;
    height: 100%;
    display: block;
    transition: transform var(--transition-speed) var(--transition-timing);
    object-fit: cover;
    object-position: center;
}

.showcase-image:hover img {
    transform: scale(1.05);
}

.image-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    background: linear-gradient(to top, rgba(18, 18, 18, 0.9), rgba(18, 18, 18, 0));
    padding: 2rem;
    transition: all var(--transition-speed) var(--transition-timing);
}

.overlay-content h3 {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
    color: var(--light-color);
}

.overlay-content p {
    color: var(--text-color-dark-secondary);
    margin: 0;
}

.showcase-content {
    flex: 1;
    min-width: 300px;
}

.showcase-content h3 {
    font-size: 2rem;
    margin-bottom: 1rem;
    color: var(--light-color);
}

.showcase-content p {
    font-size: 1.1rem;
    line-height: 1.7;
    color: var(--text-color-dark-secondary);
    margin-bottom: 2rem;
}

.showcase-stats {
    display: flex;
    flex-wrap: wrap;
    gap: 1.5rem;
    margin-top: 2rem;
}

.stat-box {
    flex: 1;
    min-width: 120px;
    background-color: var(--bg-dark-tertiary);
    padding: 1.5rem;
    border-radius: var(--border-radius);
    text-align: center;
    transition: all var(--transition-speed) var(--transition-timing);
}

.stat-box:hover {
    transform: translateY(-5px);
    background-color: var(--bg-dark-quaternary);
}

.stat-box .stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.stat-box p {
    font-size: 1rem;
    color: var(--text-color-dark);
    margin: 0;
}

/* Pricing Section */
.pricing-section {
    background-color: var(--bg-dark-secondary);
    position: relative;
}

.pricing-toggle-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin: 2rem 0 3rem;
    width: 100%;
    max-width: 300px;
    margin-left: auto;
    margin-right: auto;
}

.pricing-toggle-buttons {
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(30, 30, 30, 0.6);
    border-radius: var(--border-radius-pill);
    padding: 4px;
    margin-bottom: 1rem;
    position: relative;
    border: 1px solid var(--border-color-dark-light);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
    width: 100%;
    overflow: hidden;
}

.toggle-btn {
    flex: 1;
    background: none;
    border: none;
    padding: 10px 0;
    font-size: 0.95rem;
    font-weight: 600;
    color: var(--text-color-dark-secondary);
    cursor: pointer;
    border-radius: var(--border-radius-pill);
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
    position: relative;
    z-index: 2;
    text-align: center;
}

.toggle-btn.active {
    color: var(--bg-dark);
    font-weight: 700;
}

.toggle-btn:hover:not(.active) {
    color: var(--light-color);
}

/* Animasyonlu slider efekti */
.pricing-toggle-buttons::after {
    content: '';
    position: absolute;
    top: 4px;
    left: 4px;
    width: calc(50% - 4px);
    height: calc(100% - 8px);
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    border-radius: var(--border-radius-pill);
    transition: transform 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
    z-index: 1;
    box-shadow: 0 2px 10px rgba(67, 97, 238, 0.4);
}

.pricing-toggle-buttons.yearly-active::after {
    transform: translateX(calc(100% - 4px));
}

.pricing-toggle-info {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 0.5rem;
}

.discount-badge {
    background-color: rgba(67, 97, 238, 0.15);
    color: var(--primary-color);
    padding: 6px 12px;
    border-radius: var(--border-radius-pill);
    font-size: 0.85rem;
    font-weight: 600;
    border: 1px solid rgba(67, 97, 238, 0.3);
    box-shadow: 0 2px 8px rgba(67, 97, 238, 0.2);
    display: inline-flex;
    align-items: center;
    gap: 6px;
}

.discount-badge::before {
    content: '\f0d6';
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    font-size: 0.8rem;
}

/* Eski Fiyatlandırma Kartları Stili */
.pricing-plans {
    display: flex;
    flex-wrap: wrap;
    gap: 2rem;
    justify-content: center;
    margin: 3rem 0;
}

/* Yeni Fiyatlandırma Tablosu Stili */
.pricing-table-container {
    margin: 3rem 0;
    overflow-x: auto;
}

.pricing-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
}

.pricing-table-header {
    display: flex;
    width: 100%;
}

.pricing-table-row {
    display: flex;
    width: 100%;
}

.pricing-table-cell {
    flex: 1;
    padding: 1.5rem;
    text-align: center;
    border-bottom: 1px solid var(--border-color-dark);
    background-color: var(--bg-dark-tertiary);
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.pricing-table-cell:not(.empty-cell):not(.feature-name):hover {
    background-color: rgba(67, 97, 238, 0.05);
    transform: translateY(-2px);
    z-index: 2;
}

.pricing-table-cell:not(.empty-cell):not(.feature-name)::after {
    content: '';
    position: absolute;
    width: 100%;
    height: 2px;
    bottom: 0;
    left: 0;
    background-color: var(--primary-color);
    transform: scaleX(0);
    transform-origin: bottom right;
    transition: transform 0.3s ease;
}

.pricing-table-cell:not(.empty-cell):not(.feature-name):hover::after {
    transform: scaleX(1);
    transform-origin: bottom left;
}

.pricing-table-cell.empty-cell {
    background-color: transparent;
    border-bottom: none;
}

.pricing-table-cell.feature-name {
    text-align: left;
    justify-content: flex-start;
    font-weight: 500;
    background-color: var(--bg-dark-quaternary);
    color: var(--text-color-dark);
    border-left: 3px solid var(--primary-color);
    padding-left: 2rem;
}

.pricing-table-cell.plan-header {
    flex-direction: column;
    padding: 2rem 1.5rem;
    background-color: var(--bg-dark-quaternary);
    position: relative;
    z-index: 1;
}

.pricing-table-cell.plan-header h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: var(--light-color);
}

.pricing-table-cell.popular {
    background-color: rgba(67, 97, 238, 0.05);
    position: relative;
    z-index: 2;
    box-shadow: 0 0 20px rgba(67, 97, 238, 0.2);
}

.pricing-table-cell.popular::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(67, 97, 238, 0.05) 0%, rgba(76, 201, 240, 0.05) 100%);
    opacity: 0.8;
    z-index: -1;
}

.pricing-table-cell.plan-header.popular {
    transform: scale(1.05);
    z-index: 3;
    background-color: rgba(67, 97, 238, 0.1);
    border: 1px solid var(--primary-color);
    box-shadow: 0 10px 30px rgba(67, 97, 238, 0.3);
}

.pricing-table-cell.plan-header.popular::after {
    content: '';
    position: absolute;
    top: -30%;
    left: -30%;
    width: 160%;
    height: 160%;
    background: radial-gradient(circle, rgba(67, 97, 238, 0.2) 0%, transparent 70%);
    opacity: 0.5;
    z-index: -1;
    animation: pulse-glow 3s infinite alternate;
}

@keyframes pulse-glow {
    0% {
        opacity: 0.3;
        transform: scale(0.95);
    }
    100% {
        opacity: 0.6;
        transform: scale(1.05);
    }
}

.pricing-table-row:nth-child(odd) .pricing-table-cell:not(.feature-name):not(.empty-cell):not(.plan-header) {
    background-color: var(--bg-dark-quaternary);
}

.pricing-table-row.button-row .pricing-table-cell {
    padding: 2rem 1.5rem;
    border-bottom: none;
}

.pricing-table-row.button-row .pricing-table-cell.popular {
    padding-bottom: 2.5rem;
}

.pricing-table-row .pricing-table-cell i.fas.fa-check {
    color: var(--primary-color);
    font-size: 1.2rem;
}

.pricing-table-row .pricing-table-cell i.fas.fa-times {
    color: var(--text-color-dark-secondary);
    font-size: 1.2rem;
}

/* Responsive Tasarım */
@media (max-width: 1200px) {
    .pricing-cards-container {
        gap: 1.5rem;
    }

    .pricing-card {
        min-width: 260px;
    }
}

@media (max-width: 992px) {
    .pricing-cards-container {
        gap: 1rem;
    }

    .pricing-card {
        min-width: 240px;
    }

    .pricing-header {
        padding: 1.5rem;
    }

    .pricing-features {
        padding: 1.5rem;
    }

    .pricing-footer {
        padding: 0 1.5rem 1.5rem;
    }

    .amount {
        font-size: 3rem;
    }
}

@media (max-width: 768px) {
    .pricing-cards-container {
        flex-direction: column;
        align-items: center;
        gap: 2rem;
    }

    .pricing-card {
        min-width: 280px;
        max-width: 450px;
        width: 100%;
    }

    .pricing-card.popular {
        transform: scale(1.03);
        order: -1;
    }

    .pricing-header h3 {
        font-size: 1.8rem;
    }

    .amount {
        font-size: 3.2rem;
    }
}

/* Mobil Görünüm İçin Özel Stil */
@media (max-width: 576px) {
    .pricing-card {
        min-width: 100%;
    }

    .pricing-header {
        padding: 1.2rem;
    }

    .pricing-features {
        padding: 1.2rem;
    }

    .pricing-footer {
        padding: 0 1.2rem 1.2rem;
    }

    .pricing-header h3 {
        font-size: 1.5rem;
    }

    .amount {
        font-size: 2.8rem;
    }

    .period {
        font-size: 0.9rem;
    }

    .pricing-description {
        font-size: 0.8rem;
    }
}

/* Fiyatlandırma Kartları Stili */
.pricing-cards-container {
    display: flex;
    flex-wrap: wrap;
    gap: 2rem;
    justify-content: center;
    margin: 3rem 0;
}

.pricing-card {
    flex: 1;
    min-width: 280px;
    max-width: 350px;
    background-color: var(--bg-dark-tertiary);
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 5px 25px rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
    position: relative;
    z-index: 1;
    display: flex;
    flex-direction: column;
    margin: 0 10px;
}

.pricing-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(67, 97, 238, 0.08) 0%, rgba(76, 201, 240, 0.08) 100%);
    opacity: 0;
    transition: opacity 0.4s ease;
    z-index: -1;
}

.pricing-card::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(67, 97, 238, 0.1) 0%, transparent 70%);
    opacity: 0;
    transform: scale(0.5);
    transition: transform 0.6s ease, opacity 0.6s ease;
    z-index: -2;
}

.pricing-card:hover {
    transform: translateY(-15px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
    border-color: var(--primary-color);
}

.pricing-card:hover::before {
    opacity: 1;
}

.pricing-card:hover::after {
    opacity: 0.8;
    transform: scale(1);
}

.pricing-card.popular {
    transform: scale(1.05);
    border-color: var(--primary-color);
    z-index: 2;
    box-shadow: 0 20px 40px rgba(67, 97, 238, 0.3);
}

.pricing-card.popular:hover {
    transform: scale(1.05) translateY(-10px);
    box-shadow: 0 25px 50px rgba(67, 97, 238, 0.4);
}

.popular-badge {
    position: absolute;
    top: 0;
    right: 0;
    background-color: var(--primary-color);
    color: var(--bg-dark);
    padding: 0.5rem 1rem;
    font-size: 0.8rem;
    font-weight: 600;
    border-bottom-left-radius: var(--border-radius);
}

.pricing-header {
    padding: 2rem;
    text-align: center;
    border-bottom: 1px solid var(--border-color-dark);
    background-color: var(--bg-dark-quaternary);
}

.pricing-header h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: var(--light-color);
}

.price {
    margin: 1.5rem 0;
    position: relative;
    display: inline-block;
}

.amount {
    font-size: 3.5rem;
    font-weight: 700;
    color: var(--primary-color);
    line-height: 1;
    display: block;
}

.amount.yearly {
    display: none;
}

.period {
    font-size: 1rem;
    color: var(--text-color-dark-secondary);
    display: inline-block;
    margin-left: 0.2rem;
}

.pricing-description {
    font-size: 0.9rem;
    color: var(--text-color-dark-secondary);
    margin: 0;
}

.pricing-features {
    padding: 2rem;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 0.8rem;
    color: var(--text-color-dark);
    padding: 0.5rem 0;
    border-bottom: 1px dashed rgba(255, 255, 255, 0.05);
}

.feature-item:last-child {
    border-bottom: none;
}

.feature-item i {
    color: var(--primary-color);
    font-size: 1rem;
    min-width: 20px;
    text-align: center;
}

.feature-item.disabled {
    color: var(--text-color-dark-secondary);
    text-decoration: line-through;
}

.feature-item.disabled i {
    color: var(--text-color-dark-secondary);
}

.pricing-footer {
    padding: 0 2rem 2rem;
    text-align: center;
}

.pricing-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 1.3rem 2rem;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
    color: var(--bg-dark);
    border-radius: var(--border-radius-pill);
    font-weight: 700;
    transition: all 0.4s ease;
    text-decoration: none;
    width: 100%;
    box-shadow: 0 10px 25px rgba(67, 97, 238, 0.4);
    position: relative;
    overflow: hidden;
    text-transform: uppercase;
    letter-spacing: 0.8px;
    font-size: 1rem;
    gap: 0.8rem;
    border: 2px solid transparent;
}

.pricing-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, var(--accent-color), var(--primary-color));
    z-index: -1;
    transition: opacity 0.4s ease;
    opacity: 0;
}

.pricing-btn::after {
    content: '';
    position: absolute;
    width: 150%;
    height: 150%;
    background: linear-gradient(45deg, var(--accent-color), var(--primary-color));
    top: -25%;
    left: -25%;
    transform: translateX(-100%) rotate(45deg);
    transition: transform 0.6s ease;
    z-index: -2;
}

.pricing-btn:hover {
    background: transparent;
    transform: translateY(-8px);
    box-shadow: 0 15px 35px rgba(67, 97, 238, 0.6);
    border-color: var(--primary-color);
}

.pricing-btn:hover::before {
    opacity: 1;
}

.pricing-btn:hover::after {
    transform: translateX(0) rotate(45deg);
}

.pricing-btn:active {
    transform: translateY(-4px);
    box-shadow: 0 8px 20px rgba(67, 97, 238, 0.4);
}

.pricing-btn i {
    font-size: 0.85rem;
    transition: transform 0.3s ease;
}

.pricing-btn:hover i {
    transform: translateX(5px);
}

.pricing-note {
    text-align: center;
    max-width: 600px;
    margin: 0 auto;
}

.pricing-note p {
    color: var(--text-color-dark-secondary);
    margin-bottom: 0.5rem;
}

.pricing-note a {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    transition: color var(--transition-speed) var(--transition-timing);
}

.pricing-note a:hover {
    color: var(--accent-color);
    text-decoration: underline;
}

/* FAQ Section */
.faq-section {
    background-color: var(--bg-dark);
    position: relative;
}

.faq-container {
    max-width: 800px;
    margin: 3rem auto;
}

.faq-item {
    background-color: var(--bg-dark-tertiary);
    border-radius: var(--border-radius);
    margin-bottom: 1rem;
    overflow: hidden;
    box-shadow: var(--card-shadow-dark);
    border: 1px solid var(--border-color-dark);
    transition: all var(--transition-speed) var(--transition-timing);
}

.faq-item:hover {
    border-color: var(--border-color-dark-light);
}

.faq-question {
    padding: 1.5rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: space-between;
    transition: all var(--transition-speed) var(--transition-timing);
}

.faq-question h3 {
    font-size: 1.1rem;
    margin: 0;
    color: var(--text-color-dark);
    font-weight: 500;
    transition: color var(--transition-speed) var(--transition-timing);
}

.faq-icon {
    color: var(--primary-color);
    transition: transform var(--transition-speed) var(--transition-timing);
}

.faq-item.active .faq-icon {
    transform: rotate(180deg);
}

.faq-item.active .faq-question h3 {
    color: var(--primary-color);
}

.faq-answer {
    padding: 0 1.5rem;
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.4s ease-in-out, padding 0.4s ease-in-out, opacity 0.3s ease-in-out;
    opacity: 0;
    visibility: hidden;
}

.faq-item.active .faq-answer {
    padding: 0 1.5rem 1.5rem;
    max-height: 500px;
    opacity: 1;
    visibility: visible;
    transition: max-height 0.4s ease-in-out, padding 0.4s ease-in-out, opacity 0.3s ease-in-out 0.1s, visibility 0s;
}

.faq-answer p {
    margin: 0;
    color: var(--text-color-dark-secondary);
    line-height: 1.7;
}

.faq-cta {
    text-align: center;
    margin-top: 3rem;
}

.faq-cta p {
    font-size: 1.1rem;
    color: var(--text-color-dark);
    margin-bottom: 1rem;
}

/* Contact Form */
.contact-form-container {
    max-width: 800px;
    margin: 3rem auto;
    background-color: var(--bg-dark-tertiary);
    padding: 2rem;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--card-shadow-dark);
    border: 1px solid var(--border-color-dark);
}

.contact-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.form-row {
    display: flex;
    flex-wrap: wrap;
    gap: 1.5rem;
}

.form-group {
    flex: 1;
    min-width: 250px;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    color: var(--text-color-dark);
    font-weight: 500;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 1rem;
    background-color: var(--bg-dark-quaternary);
    border: 1px solid var(--border-color-dark);
    border-radius: var(--border-radius);
    color: var(--text-color-dark);
    font-family: inherit;
    font-size: 1rem;
    transition: all var(--transition-speed) var(--transition-timing);
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(67, 97, 238, 0.2);
}

.form-group textarea {
    min-height: 150px;
    resize: vertical;
}

.form-submit {
    text-align: center;
    margin-top: 1rem;
}

/* Back to Top Button */
.back-to-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 50px;
    height: 50px;
    background-color: var(--primary-color);
    color: var(--bg-dark);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    transition: all var(--transition-speed) var(--transition-timing);
    opacity: 0;
    visibility: hidden;
    z-index: 999;
}

.back-to-top.visible {
    opacity: 1;
    visibility: visible;
}

.back-to-top:hover {
    background-color: var(--primary-color-dark);
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
}

/* Footer App Badges */
.footer-app-badges {
    display: flex;
    gap: 1rem;
    margin-top: 1.5rem;
}

.app-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.8rem 1.2rem;
    background-color: var(--bg-dark-tertiary);
    border-radius: var(--border-radius);
    color: var(--text-color-dark);
    font-weight: 500;
    transition: all var(--transition-speed) var(--transition-timing);
}

.app-badge i {
    font-size: 1.2rem;
    color: var(--primary-color);
}

.app-badge:hover {
    background-color: var(--bg-dark-quaternary);
    transform: translateY(-3px);
    color: var(--text-color-dark);
}

/* Footer Bottom Links */
.footer-bottom {
    padding: 1.5rem 0;
}

.footer-bottom .container {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: center;
    gap: 1rem;
}

.footer-bottom-links {
    display: flex;
    gap: 1.5rem;
}

.footer-bottom-links a {
    color: var(--text-color-dark-secondary);
    font-size: 0.9rem;
    transition: color var(--transition-speed) var(--transition-timing);
}

.footer-bottom-links a:hover {
    color: var(--primary-color);
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .pricing-card.popular {
        transform: scale(1);
    }

    .pricing-card.popular:hover {
        transform: translateY(-10px);
    }

    .tab-content-inner {
        flex-direction: column;
    }

    .tab-image {
        order: -1;
    }

    .benefits-showcase {
        flex-direction: column;
    }

    .footer-bottom .container {
        flex-direction: column;
        text-align: center;
    }

    .footer-bottom-links {
        justify-content: center;
    }
}
