-- Transaction Soft Delete Migration
-- <PERSON>u script Transaction tablosuna soft delete alanlar<PERSON>n<PERSON> ekler
-- Tarih: 2025-06-27
-- Amaç: Transaction kayıtlarının soft delete ile silinebilmesi için gerekli alanları ekler

-- Transaction tablosuna soft delete alanlarını ekle
ALTER TABLE Transactions 
ADD IsActive BIT NOT NULL DEFAULT 1;

ALTER TABLE Transactions 
ADD CreationDate DATETIME2 NOT NULL DEFAULT GETDATE();

ALTER TABLE Transactions 
ADD UpdatedDate DATETIME2 NULL;

ALTER TABLE Transactions 
ADD DeletedDate DATETIME2 NULL;

-- Mevcut kayıtları güncelle (tüm mevcut kayıtlar aktif olarak işaretlenir)
UPDATE Transactions 
SET IsActive = 1, 
    CreationDate = ISNULL(TransactionDate, GETDATE())
WHERE IsActive IS NULL OR CreationDate IS NULL;

-- Performans için index oluştur
CREATE NONCLUSTERED INDEX IX_Transactions_IsActive_CompanyID 
ON Transactions (IsActive, CompanyID) 
INCLUDE (TransactionID, MemberID, TransactionDate);

-- Soft delete için index
CREATE NONCLUSTERED INDEX IX_Transactions_DeletedDate 
ON Transactions (DeletedDate) 
WHERE DeletedDate IS NOT NULL;

PRINT 'Transaction Soft Delete Migration completed successfully!';
