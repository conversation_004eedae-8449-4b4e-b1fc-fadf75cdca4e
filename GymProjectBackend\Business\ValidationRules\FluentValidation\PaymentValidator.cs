﻿using Entities.Concrete;
using FluentValidation;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Business.ValidationRules.FluentValidation
{
    public class PaymentValidator:AbstractValidator<Payment>
    {
        public PaymentValidator()
        {
            RuleFor(x => x.MemberShipID).NotEmpty().WithMessage("Üye adı kısmı boş bırakılamaz.");
            RuleFor(x => x.PaymentAmount).NotEmpty().WithMessage("Fiyat kısmı boş bırakılamaz.");
            RuleFor(x => x.PaymentMethod).NotEmpty().WithMessage("Ödeme türü kısmı boş bırakılamaz.");
            RuleFor(x => x.PaymentDate).NotEmpty().WithMessage("Ödeme tarihi kısmı boş bırakılamaz.");
        }
    }
}
