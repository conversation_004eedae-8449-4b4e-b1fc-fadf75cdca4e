﻿using Core.Entities.Concrete;
using Core.Utilities.Results;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Business.Abstract
{
    public interface IUserDeviceService
    {
        IDataResult<List<UserDevice>> GetActiveDevicesByUserId(int userId);
        IResult Add(UserDevice device);
        IResult Update(UserDevice device);
        IResult Delete(int id);
        IDataResult<UserDevice> GetByRefreshToken(string refreshToken);
        IResult RevokeDevice(int deviceId);
        IResult RevokeAllDevicesExceptCurrent(int userId, string currentRefreshToken);
    }
}
