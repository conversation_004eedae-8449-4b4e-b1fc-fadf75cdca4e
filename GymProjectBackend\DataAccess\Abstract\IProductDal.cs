﻿using Core.DataAccess;
using Core.Utilities.Paging;
using Core.Utilities.Results;
using Entities.Concrete;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DataAccess.Abstract
{
    public interface IProductDal : IEntityRepository<Product>
    {
        PaginatedResult<Product> GetAllPaginated(ProductPagingParameters parameters);

        // SOLID prensiplerine uygun: Complex business operations
        IResult SoftDeleteProduct(int productId, int companyId);
        IResult UpdateProductWithBusinessLogic(Product product, int companyId);

        // SOLID prensiplerine uygun: Validation logic DAL katmanında
        IDataResult<Product> GetProductByIdWithValidation(int productId, int companyId);
    }
}
