using System;

namespace Core.Utilities.Paging
{
    public class ExpensePagingParameters : PagingParameters
    {
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public string? ExpenseType { get; set; }
        public decimal? MinAmount { get; set; }
        public decimal? MaxAmount { get; set; }
        public bool? IsActive { get; set; }

        public ExpensePagingParameters()
        {
            PageSize = 20; // Varsayılan sayfa boyutu
            PageNumber = 1; // Varsayılan sayfa numarası
            SearchText = ""; // Boş arama metni
            SortBy = "ExpenseDate"; // Varsayılan sıralama alanı
            SortDirection = "desc"; // Varsayılan sıralama yönü (en yeni önce)
        }
    }
}
