{"version": 3, "sources": ["../../../../../../node_modules/file-saver/dist/FileSaver.min.js"], "sourcesContent": ["(function (a, b) {\n  if (\"function\" == typeof define && define.amd) define([], b);else if (\"undefined\" != typeof exports) b();else {\n    b(), a.FileSaver = {\n      exports: {}\n    }.exports;\n  }\n})(this, function () {\n  \"use strict\";\n\n  function b(a, b) {\n    return \"undefined\" == typeof b ? b = {\n      autoBom: !1\n    } : \"object\" != typeof b && (console.warn(\"Deprecated: Expected third argument to be a object\"), b = {\n      autoBom: !b\n    }), b.autoBom && /^\\s*(?:text\\/\\S*|application\\/xml|\\S*\\/\\S*\\+xml)\\s*;.*charset\\s*=\\s*utf-8/i.test(a.type) ? new Blob([\"\\uFEFF\", a], {\n      type: a.type\n    }) : a;\n  }\n  function c(a, b, c) {\n    var d = new XMLHttpRequest();\n    d.open(\"GET\", a), d.responseType = \"blob\", d.onload = function () {\n      g(d.response, b, c);\n    }, d.onerror = function () {\n      console.error(\"could not download file\");\n    }, d.send();\n  }\n  function d(a) {\n    var b = new XMLHttpRequest();\n    b.open(\"HEAD\", a, !1);\n    try {\n      b.send();\n    } catch (a) {}\n    return 200 <= b.status && 299 >= b.status;\n  }\n  function e(a) {\n    try {\n      a.dispatchEvent(new MouseEvent(\"click\"));\n    } catch (c) {\n      var b = document.createEvent(\"MouseEvents\");\n      b.initMouseEvent(\"click\", !0, !0, window, 0, 0, 0, 80, 20, !1, !1, !1, !1, 0, null), a.dispatchEvent(b);\n    }\n  }\n  var f = \"object\" == typeof window && window.window === window ? window : \"object\" == typeof self && self.self === self ? self : \"object\" == typeof global && global.global === global ? global : void 0,\n    a = f.navigator && /Macintosh/.test(navigator.userAgent) && /AppleWebKit/.test(navigator.userAgent) && !/Safari/.test(navigator.userAgent),\n    g = f.saveAs || (\"object\" != typeof window || window !== f ? function () {} : \"download\" in HTMLAnchorElement.prototype && !a ? function (b, g, h) {\n      var i = f.URL || f.webkitURL,\n        j = document.createElement(\"a\");\n      g = g || b.name || \"download\", j.download = g, j.rel = \"noopener\", \"string\" == typeof b ? (j.href = b, j.origin === location.origin ? e(j) : d(j.href) ? c(b, g, h) : e(j, j.target = \"_blank\")) : (j.href = i.createObjectURL(b), setTimeout(function () {\n        i.revokeObjectURL(j.href);\n      }, 4E4), setTimeout(function () {\n        e(j);\n      }, 0));\n    } : \"msSaveOrOpenBlob\" in navigator ? function (f, g, h) {\n      if (g = g || f.name || \"download\", \"string\" != typeof f) navigator.msSaveOrOpenBlob(b(f, h), g);else if (d(f)) c(f, g, h);else {\n        var i = document.createElement(\"a\");\n        i.href = f, i.target = \"_blank\", setTimeout(function () {\n          e(i);\n        });\n      }\n    } : function (b, d, e, g) {\n      if (g = g || open(\"\", \"_blank\"), g && (g.document.title = g.document.body.innerText = \"downloading...\"), \"string\" == typeof b) return c(b, d, e);\n      var h = \"application/octet-stream\" === b.type,\n        i = /constructor/i.test(f.HTMLElement) || f.safari,\n        j = /CriOS\\/[\\d]+/.test(navigator.userAgent);\n      if ((j || h && i || a) && \"undefined\" != typeof FileReader) {\n        var k = new FileReader();\n        k.onloadend = function () {\n          var a = k.result;\n          a = j ? a : a.replace(/^data:[^;]*;/, \"data:attachment/file;\"), g ? g.location.href = a : location = a, g = null;\n        }, k.readAsDataURL(b);\n      } else {\n        var l = f.URL || f.webkitURL,\n          m = l.createObjectURL(b);\n        g ? g.location = m : location.href = m, g = null, setTimeout(function () {\n          l.revokeObjectURL(m);\n        }, 4E4);\n      }\n    });\n  f.saveAs = g.saveAs = g, \"undefined\" != typeof module && (module.exports = g);\n});\n\n"], "mappings": ";;;;;AAAA;AAAA;AAAA,KAAC,SAAU,GAAG,GAAG;AACf,UAAI,cAAc,OAAO,UAAU,OAAO,IAAK,QAAO,CAAC,GAAG,CAAC;AAAA,eAAW,eAAe,OAAO,QAAS,GAAE;AAAA,WAAO;AAC5G,UAAE,GAAG,EAAE,YAAY;AAAA,UACjB,SAAS,CAAC;AAAA,QACZ,EAAE;AAAA,MACJ;AAAA,IACF,GAAG,SAAM,WAAY;AACnB;AAEA,eAAS,EAAEA,IAAGC,IAAG;AACf,eAAO,eAAe,OAAOA,KAAIA,KAAI;AAAA,UACnC,SAAS;AAAA,QACX,IAAI,YAAY,OAAOA,OAAM,QAAQ,KAAK,oDAAoD,GAAGA,KAAI;AAAA,UACnG,SAAS,CAACA;AAAA,QACZ,IAAIA,GAAE,WAAW,6EAA6E,KAAKD,GAAE,IAAI,IAAI,IAAI,KAAK,CAAC,UAAUA,EAAC,GAAG;AAAA,UACnI,MAAMA,GAAE;AAAA,QACV,CAAC,IAAIA;AAAA,MACP;AACA,eAAS,EAAEA,IAAGC,IAAGC,IAAG;AAClB,YAAIC,KAAI,IAAI,eAAe;AAC3B,QAAAA,GAAE,KAAK,OAAOH,EAAC,GAAGG,GAAE,eAAe,QAAQA,GAAE,SAAS,WAAY;AAChE,YAAEA,GAAE,UAAUF,IAAGC,EAAC;AAAA,QACpB,GAAGC,GAAE,UAAU,WAAY;AACzB,kBAAQ,MAAM,yBAAyB;AAAA,QACzC,GAAGA,GAAE,KAAK;AAAA,MACZ;AACA,eAAS,EAAEH,IAAG;AACZ,YAAIC,KAAI,IAAI,eAAe;AAC3B,QAAAA,GAAE,KAAK,QAAQD,IAAG,KAAE;AACpB,YAAI;AACF,UAAAC,GAAE,KAAK;AAAA,QACT,SAASD,IAAG;AAAA,QAAC;AACb,eAAO,OAAOC,GAAE,UAAU,OAAOA,GAAE;AAAA,MACrC;AACA,eAAS,EAAED,IAAG;AACZ,YAAI;AACF,UAAAA,GAAE,cAAc,IAAI,WAAW,OAAO,CAAC;AAAA,QACzC,SAASE,IAAG;AACV,cAAID,KAAI,SAAS,YAAY,aAAa;AAC1C,UAAAA,GAAE,eAAe,SAAS,MAAI,MAAI,QAAQ,GAAG,GAAG,GAAG,IAAI,IAAI,OAAI,OAAI,OAAI,OAAI,GAAG,IAAI,GAAGD,GAAE,cAAcC,EAAC;AAAA,QACxG;AAAA,MACF;AACA,UAAI,IAAI,YAAY,OAAO,UAAU,OAAO,WAAW,SAAS,SAAS,YAAY,OAAO,QAAQ,KAAK,SAAS,OAAO,OAAO,YAAY,OAAO,UAAU,OAAO,WAAW,SAAS,SAAS,QAC/L,IAAI,EAAE,aAAa,YAAY,KAAK,UAAU,SAAS,KAAK,cAAc,KAAK,UAAU,SAAS,KAAK,CAAC,SAAS,KAAK,UAAU,SAAS,GACzI,IAAI,EAAE,WAAW,YAAY,OAAO,UAAU,WAAW,IAAI,WAAY;AAAA,MAAC,IAAI,cAAc,kBAAkB,aAAa,CAAC,IAAI,SAAUA,IAAGG,IAAG,GAAG;AACjJ,YAAI,IAAI,EAAE,OAAO,EAAE,WACjB,IAAI,SAAS,cAAc,GAAG;AAChC,QAAAA,KAAIA,MAAKH,GAAE,QAAQ,YAAY,EAAE,WAAWG,IAAG,EAAE,MAAM,YAAY,YAAY,OAAOH,MAAK,EAAE,OAAOA,IAAG,EAAE,WAAW,SAAS,SAAS,EAAE,CAAC,IAAI,EAAE,EAAE,IAAI,IAAI,EAAEA,IAAGG,IAAG,CAAC,IAAI,EAAE,GAAG,EAAE,SAAS,QAAQ,MAAM,EAAE,OAAO,EAAE,gBAAgBH,EAAC,GAAG,WAAW,WAAY;AACxP,YAAE,gBAAgB,EAAE,IAAI;AAAA,QAC1B,GAAG,GAAG,GAAG,WAAW,WAAY;AAC9B,YAAE,CAAC;AAAA,QACL,GAAG,CAAC;AAAA,MACN,IAAI,sBAAsB,YAAY,SAAUI,IAAGD,IAAG,GAAG;AACvD,YAAIA,KAAIA,MAAKC,GAAE,QAAQ,YAAY,YAAY,OAAOA,GAAG,WAAU,iBAAiB,EAAEA,IAAG,CAAC,GAAGD,EAAC;AAAA,iBAAW,EAAEC,EAAC,EAAG,GAAEA,IAAGD,IAAG,CAAC;AAAA,aAAO;AAC7H,cAAI,IAAI,SAAS,cAAc,GAAG;AAClC,YAAE,OAAOC,IAAG,EAAE,SAAS,UAAU,WAAW,WAAY;AACtD,cAAE,CAAC;AAAA,UACL,CAAC;AAAA,QACH;AAAA,MACF,IAAI,SAAUJ,IAAGE,IAAGG,IAAGF,IAAG;AACxB,YAAIA,KAAIA,MAAK,KAAK,IAAI,QAAQ,GAAGA,OAAMA,GAAE,SAAS,QAAQA,GAAE,SAAS,KAAK,YAAY,mBAAmB,YAAY,OAAOH,GAAG,QAAO,EAAEA,IAAGE,IAAGG,EAAC;AAC/I,YAAI,IAAI,+BAA+BL,GAAE,MACvC,IAAI,eAAe,KAAK,EAAE,WAAW,KAAK,EAAE,QAC5C,IAAI,eAAe,KAAK,UAAU,SAAS;AAC7C,aAAK,KAAK,KAAK,KAAK,MAAM,eAAe,OAAO,YAAY;AAC1D,cAAI,IAAI,IAAI,WAAW;AACvB,YAAE,YAAY,WAAY;AACxB,gBAAID,KAAI,EAAE;AACV,YAAAA,KAAI,IAAIA,KAAIA,GAAE,QAAQ,gBAAgB,uBAAuB,GAAGI,KAAIA,GAAE,SAAS,OAAOJ,KAAI,WAAWA,IAAGI,KAAI;AAAA,UAC9G,GAAG,EAAE,cAAcH,EAAC;AAAA,QACtB,OAAO;AACL,cAAI,IAAI,EAAE,OAAO,EAAE,WACjB,IAAI,EAAE,gBAAgBA,EAAC;AACzB,UAAAG,KAAIA,GAAE,WAAW,IAAI,SAAS,OAAO,GAAGA,KAAI,MAAM,WAAW,WAAY;AACvE,cAAE,gBAAgB,CAAC;AAAA,UACrB,GAAG,GAAG;AAAA,QACR;AAAA,MACF;AACF,QAAE,SAAS,EAAE,SAAS,GAAG,eAAe,OAAO,WAAW,OAAO,UAAU;AAAA,IAC7E,CAAC;AAAA;AAAA;", "names": ["a", "b", "c", "d", "g", "f", "e"]}