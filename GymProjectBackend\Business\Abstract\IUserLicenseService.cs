﻿using Core.Utilities.Results;
using Entities.Concrete;
using Entities.DTOs;

namespace Business.Abstract
{
    public interface IUserLicenseService
    {
        IDataResult<List<UserLicenseDto>> GetAll();
        IDataResult<PaginatedUserLicenseDto> GetAllPaginated(int page, int pageSize, string searchTerm, string sortBy, string companyName, int? remainingDaysMin, int? remainingDaysMax);
        IDataResult<PaginatedUserLicenseDto> GetExpiredAndPassive(int page, int pageSize, string searchTerm);
        IDataResult<UserLicenseDto> GetById(int id);
        IDataResult<List<UserLicenseDto>> GetActiveByUserId(int userId);
        IDataResult<List<UserLicenseDto>> GetMyActiveLicenses(int userId);
        IResult Add(UserLicense userLicense);
        IResult Update(UserLicense userLicense);
        IResult Delete(int id);
        IResult Purchase(LicensePurchaseDto licensePurchaseDto);

        IResult ExtendLicenseByPackage(LicenseExtensionByPackageDto licenseExtensionByPackageDto);
        IResult RevokeLicense(int userLicenseId);
        IDataResult<List<string>> GetUserRoles(int userId);
    }

}
