{"version": 3, "sources": ["../../../../../../node_modules/@angular/cdk/fesm2022/text-field.mjs", "../../../../../../node_modules/@angular/material/fesm2022/input.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Component, ChangeDetectionStrategy, ViewEncapsulation, inject, NgZone, RendererFactory2, Injectable, ElementRef, EventEmitter, Directive, Output, Renderer2, booleanAttribute, Input, NgModule } from '@angular/core';\nimport { EMPTY, Subject } from 'rxjs';\nimport { P as Platform } from './platform-20fc4de8.mjs';\nimport { _ as _CdkPrivateStyleLoader } from './style-loader-09eecacc.mjs';\nimport { _ as _bindEventWithOptions } from './backwards-compatibility-08253a84.mjs';\nimport { c as coerceElement, a as coerceNumberProperty } from './element-15999318.mjs';\nimport { DOCUMENT } from '@angular/common';\nimport { auditTime } from 'rxjs/operators';\n\n/** Component used to load the structural styles of the text field. */\nclass _CdkTextFieldStyleLoader {\n  static ɵfac = function _CdkTextFieldStyleLoader_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || _CdkTextFieldStyleLoader)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: _CdkTextFieldStyleLoader,\n    selectors: [[\"ng-component\"]],\n    hostAttrs: [\"cdk-text-field-style-loader\", \"\"],\n    decls: 0,\n    vars: 0,\n    template: function _CdkTextFieldStyleLoader_Template(rf, ctx) {},\n    styles: [\"textarea.cdk-textarea-autosize{resize:none}textarea.cdk-textarea-autosize-measuring{padding:2px 0 !important;box-sizing:content-box !important;height:auto !important;overflow:hidden !important}textarea.cdk-textarea-autosize-measuring-firefox{padding:2px 0 !important;box-sizing:content-box !important;height:0 !important}@keyframes cdk-text-field-autofill-start{/*!*/}@keyframes cdk-text-field-autofill-end{/*!*/}.cdk-text-field-autofill-monitored:-webkit-autofill{animation:cdk-text-field-autofill-start 0s 1ms}.cdk-text-field-autofill-monitored:not(:-webkit-autofill){animation:cdk-text-field-autofill-end 0s 1ms}\"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(_CdkTextFieldStyleLoader, [{\n    type: Component,\n    args: [{\n      template: '',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      host: {\n        'cdk-text-field-style-loader': ''\n      },\n      styles: [\"textarea.cdk-textarea-autosize{resize:none}textarea.cdk-textarea-autosize-measuring{padding:2px 0 !important;box-sizing:content-box !important;height:auto !important;overflow:hidden !important}textarea.cdk-textarea-autosize-measuring-firefox{padding:2px 0 !important;box-sizing:content-box !important;height:0 !important}@keyframes cdk-text-field-autofill-start{/*!*/}@keyframes cdk-text-field-autofill-end{/*!*/}.cdk-text-field-autofill-monitored:-webkit-autofill{animation:cdk-text-field-autofill-start 0s 1ms}.cdk-text-field-autofill-monitored:not(:-webkit-autofill){animation:cdk-text-field-autofill-end 0s 1ms}\"]\n    }]\n  }], null, null);\n})();\n\n/** Options to pass to the animationstart listener. */\nconst listenerOptions = {\n  passive: true\n};\n/**\n * An injectable service that can be used to monitor the autofill state of an input.\n * Based on the following blog post:\n * https://medium.com/@brunn/detecting-autofilled-fields-in-javascript-aed598d25da7\n */\nclass AutofillMonitor {\n  _platform = inject(Platform);\n  _ngZone = inject(NgZone);\n  _renderer = inject(RendererFactory2).createRenderer(null, null);\n  _styleLoader = inject(_CdkPrivateStyleLoader);\n  _monitoredElements = new Map();\n  constructor() {}\n  monitor(elementOrRef) {\n    if (!this._platform.isBrowser) {\n      return EMPTY;\n    }\n    this._styleLoader.load(_CdkTextFieldStyleLoader);\n    const element = coerceElement(elementOrRef);\n    const info = this._monitoredElements.get(element);\n    if (info) {\n      return info.subject;\n    }\n    const subject = new Subject();\n    const cssClass = 'cdk-text-field-autofilled';\n    const listener = event => {\n      // Animation events fire on initial element render, we check for the presence of the autofill\n      // CSS class to make sure this is a real change in state, not just the initial render before\n      // we fire off events.\n      if (event.animationName === 'cdk-text-field-autofill-start' && !element.classList.contains(cssClass)) {\n        element.classList.add(cssClass);\n        this._ngZone.run(() => subject.next({\n          target: event.target,\n          isAutofilled: true\n        }));\n      } else if (event.animationName === 'cdk-text-field-autofill-end' && element.classList.contains(cssClass)) {\n        element.classList.remove(cssClass);\n        this._ngZone.run(() => subject.next({\n          target: event.target,\n          isAutofilled: false\n        }));\n      }\n    };\n    const unlisten = this._ngZone.runOutsideAngular(() => {\n      element.classList.add('cdk-text-field-autofill-monitored');\n      return _bindEventWithOptions(this._renderer, element, 'animationstart', listener, listenerOptions);\n    });\n    this._monitoredElements.set(element, {\n      subject,\n      unlisten\n    });\n    return subject;\n  }\n  stopMonitoring(elementOrRef) {\n    const element = coerceElement(elementOrRef);\n    const info = this._monitoredElements.get(element);\n    if (info) {\n      info.unlisten();\n      info.subject.complete();\n      element.classList.remove('cdk-text-field-autofill-monitored');\n      element.classList.remove('cdk-text-field-autofilled');\n      this._monitoredElements.delete(element);\n    }\n  }\n  ngOnDestroy() {\n    this._monitoredElements.forEach((_info, element) => this.stopMonitoring(element));\n  }\n  static ɵfac = function AutofillMonitor_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || AutofillMonitor)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: AutofillMonitor,\n    factory: AutofillMonitor.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AutofillMonitor, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\n/** A directive that can be used to monitor the autofill state of an input. */\nclass CdkAutofill {\n  _elementRef = inject(ElementRef);\n  _autofillMonitor = inject(AutofillMonitor);\n  /** Emits when the autofill state of the element changes. */\n  cdkAutofill = new EventEmitter();\n  constructor() {}\n  ngOnInit() {\n    this._autofillMonitor.monitor(this._elementRef).subscribe(event => this.cdkAutofill.emit(event));\n  }\n  ngOnDestroy() {\n    this._autofillMonitor.stopMonitoring(this._elementRef);\n  }\n  static ɵfac = function CdkAutofill_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || CdkAutofill)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: CdkAutofill,\n    selectors: [[\"\", \"cdkAutofill\", \"\"]],\n    outputs: {\n      cdkAutofill: \"cdkAutofill\"\n    }\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkAutofill, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkAutofill]'\n    }]\n  }], () => [], {\n    cdkAutofill: [{\n      type: Output\n    }]\n  });\n})();\n\n/** Directive to automatically resize a textarea to fit its content. */\nclass CdkTextareaAutosize {\n  _elementRef = inject(ElementRef);\n  _platform = inject(Platform);\n  _ngZone = inject(NgZone);\n  _renderer = inject(Renderer2);\n  _resizeEvents = new Subject();\n  /** Keep track of the previous textarea value to avoid resizing when the value hasn't changed. */\n  _previousValue;\n  _initialHeight;\n  _destroyed = new Subject();\n  _listenerCleanups;\n  _minRows;\n  _maxRows;\n  _enabled = true;\n  /**\n   * Value of minRows as of last resize. If the minRows has decreased, the\n   * height of the textarea needs to be recomputed to reflect the new minimum. The maxHeight\n   * does not have the same problem because it does not affect the textarea's scrollHeight.\n   */\n  _previousMinRows = -1;\n  _textareaElement;\n  /** Minimum amount of rows in the textarea. */\n  get minRows() {\n    return this._minRows;\n  }\n  set minRows(value) {\n    this._minRows = coerceNumberProperty(value);\n    this._setMinHeight();\n  }\n  /** Maximum amount of rows in the textarea. */\n  get maxRows() {\n    return this._maxRows;\n  }\n  set maxRows(value) {\n    this._maxRows = coerceNumberProperty(value);\n    this._setMaxHeight();\n  }\n  /** Whether autosizing is enabled or not */\n  get enabled() {\n    return this._enabled;\n  }\n  set enabled(value) {\n    // Only act if the actual value changed. This specifically helps to not run\n    // resizeToFitContent too early (i.e. before ngAfterViewInit)\n    if (this._enabled !== value) {\n      (this._enabled = value) ? this.resizeToFitContent(true) : this.reset();\n    }\n  }\n  get placeholder() {\n    return this._textareaElement.placeholder;\n  }\n  set placeholder(value) {\n    this._cachedPlaceholderHeight = undefined;\n    if (value) {\n      this._textareaElement.setAttribute('placeholder', value);\n    } else {\n      this._textareaElement.removeAttribute('placeholder');\n    }\n    this._cacheTextareaPlaceholderHeight();\n  }\n  /** Cached height of a textarea with a single row. */\n  _cachedLineHeight;\n  /** Cached height of a textarea with only the placeholder. */\n  _cachedPlaceholderHeight;\n  /** Cached scroll top of a textarea */\n  _cachedScrollTop;\n  /** Used to reference correct document/window */\n  _document = inject(DOCUMENT, {\n    optional: true\n  });\n  _hasFocus;\n  _isViewInited = false;\n  constructor() {\n    const styleLoader = inject(_CdkPrivateStyleLoader);\n    styleLoader.load(_CdkTextFieldStyleLoader);\n    this._textareaElement = this._elementRef.nativeElement;\n  }\n  /** Sets the minimum height of the textarea as determined by minRows. */\n  _setMinHeight() {\n    const minHeight = this.minRows && this._cachedLineHeight ? `${this.minRows * this._cachedLineHeight}px` : null;\n    if (minHeight) {\n      this._textareaElement.style.minHeight = minHeight;\n    }\n  }\n  /** Sets the maximum height of the textarea as determined by maxRows. */\n  _setMaxHeight() {\n    const maxHeight = this.maxRows && this._cachedLineHeight ? `${this.maxRows * this._cachedLineHeight}px` : null;\n    if (maxHeight) {\n      this._textareaElement.style.maxHeight = maxHeight;\n    }\n  }\n  ngAfterViewInit() {\n    if (this._platform.isBrowser) {\n      // Remember the height which we started with in case autosizing is disabled\n      this._initialHeight = this._textareaElement.style.height;\n      this.resizeToFitContent();\n      this._ngZone.runOutsideAngular(() => {\n        this._listenerCleanups = [this._renderer.listen('window', 'resize', () => this._resizeEvents.next()), this._renderer.listen(this._textareaElement, 'focus', this._handleFocusEvent), this._renderer.listen(this._textareaElement, 'blur', this._handleFocusEvent)];\n        this._resizeEvents.pipe(auditTime(16)).subscribe(() => {\n          // Clear the cached heights since the styles can change\n          // when the window is resized (e.g. by media queries).\n          this._cachedLineHeight = this._cachedPlaceholderHeight = undefined;\n          this.resizeToFitContent(true);\n        });\n      });\n      this._isViewInited = true;\n      this.resizeToFitContent(true);\n    }\n  }\n  ngOnDestroy() {\n    this._listenerCleanups?.forEach(cleanup => cleanup());\n    this._resizeEvents.complete();\n    this._destroyed.next();\n    this._destroyed.complete();\n  }\n  /**\n   * Cache the height of a single-row textarea if it has not already been cached.\n   *\n   * We need to know how large a single \"row\" of a textarea is in order to apply minRows and\n   * maxRows. For the initial version, we will assume that the height of a single line in the\n   * textarea does not ever change.\n   */\n  _cacheTextareaLineHeight() {\n    if (this._cachedLineHeight) {\n      return;\n    }\n    // Use a clone element because we have to override some styles.\n    const textareaClone = this._textareaElement.cloneNode(false);\n    const cloneStyles = textareaClone.style;\n    textareaClone.rows = 1;\n    // Use `position: absolute` so that this doesn't cause a browser layout and use\n    // `visibility: hidden` so that nothing is rendered. Clear any other styles that\n    // would affect the height.\n    cloneStyles.position = 'absolute';\n    cloneStyles.visibility = 'hidden';\n    cloneStyles.border = 'none';\n    cloneStyles.padding = '0';\n    cloneStyles.height = '';\n    cloneStyles.minHeight = '';\n    cloneStyles.maxHeight = '';\n    // App styles might be messing with the height through the positioning properties.\n    cloneStyles.top = cloneStyles.bottom = cloneStyles.left = cloneStyles.right = 'auto';\n    // In Firefox it happens that textarea elements are always bigger than the specified amount\n    // of rows. This is because Firefox tries to add extra space for the horizontal scrollbar.\n    // As a workaround that removes the extra space for the scrollbar, we can just set overflow\n    // to hidden. This ensures that there is no invalid calculation of the line height.\n    // See Firefox bug report: https://bugzilla.mozilla.org/show_bug.cgi?id=33654\n    cloneStyles.overflow = 'hidden';\n    this._textareaElement.parentNode.appendChild(textareaClone);\n    this._cachedLineHeight = textareaClone.clientHeight;\n    textareaClone.remove();\n    // Min and max heights have to be re-calculated if the cached line height changes\n    this._setMinHeight();\n    this._setMaxHeight();\n  }\n  _measureScrollHeight() {\n    const element = this._textareaElement;\n    const previousMargin = element.style.marginBottom || '';\n    const isFirefox = this._platform.FIREFOX;\n    const needsMarginFiller = isFirefox && this._hasFocus;\n    const measuringClass = isFirefox ? 'cdk-textarea-autosize-measuring-firefox' : 'cdk-textarea-autosize-measuring';\n    // In some cases the page might move around while we're measuring the `textarea` on Firefox. We\n    // work around it by assigning a temporary margin with the same height as the `textarea` so that\n    // it occupies the same amount of space. See #23233.\n    if (needsMarginFiller) {\n      element.style.marginBottom = `${element.clientHeight}px`;\n    }\n    // Reset the textarea height to auto in order to shrink back to its default size.\n    // Also temporarily force overflow:hidden, so scroll bars do not interfere with calculations.\n    element.classList.add(measuringClass);\n    // The measuring class includes a 2px padding to workaround an issue with Chrome,\n    // so we account for that extra space here by subtracting 4 (2px top + 2px bottom).\n    const scrollHeight = element.scrollHeight - 4;\n    element.classList.remove(measuringClass);\n    if (needsMarginFiller) {\n      element.style.marginBottom = previousMargin;\n    }\n    return scrollHeight;\n  }\n  _cacheTextareaPlaceholderHeight() {\n    if (!this._isViewInited || this._cachedPlaceholderHeight != undefined) {\n      return;\n    }\n    if (!this.placeholder) {\n      this._cachedPlaceholderHeight = 0;\n      return;\n    }\n    const value = this._textareaElement.value;\n    this._textareaElement.value = this._textareaElement.placeholder;\n    this._cachedPlaceholderHeight = this._measureScrollHeight();\n    this._textareaElement.value = value;\n  }\n  /** Handles `focus` and `blur` events. */\n  _handleFocusEvent = event => {\n    this._hasFocus = event.type === 'focus';\n  };\n  ngDoCheck() {\n    if (this._platform.isBrowser) {\n      this.resizeToFitContent();\n    }\n  }\n  /**\n   * Resize the textarea to fit its content.\n   * @param force Whether to force a height recalculation. By default the height will be\n   *    recalculated only if the value changed since the last call.\n   */\n  resizeToFitContent(force = false) {\n    // If autosizing is disabled, just skip everything else\n    if (!this._enabled) {\n      return;\n    }\n    this._cacheTextareaLineHeight();\n    this._cacheTextareaPlaceholderHeight();\n    this._cachedScrollTop = this._textareaElement.scrollTop;\n    // If we haven't determined the line-height yet, we know we're still hidden and there's no point\n    // in checking the height of the textarea.\n    if (!this._cachedLineHeight) {\n      return;\n    }\n    const textarea = this._elementRef.nativeElement;\n    const value = textarea.value;\n    // Only resize if the value or minRows have changed since these calculations can be expensive.\n    if (!force && this._minRows === this._previousMinRows && value === this._previousValue) {\n      return;\n    }\n    const scrollHeight = this._measureScrollHeight();\n    const height = Math.max(scrollHeight, this._cachedPlaceholderHeight || 0);\n    // Use the scrollHeight to know how large the textarea *would* be if fit its entire value.\n    textarea.style.height = `${height}px`;\n    this._ngZone.runOutsideAngular(() => {\n      if (typeof requestAnimationFrame !== 'undefined') {\n        requestAnimationFrame(() => this._scrollToCaretPosition(textarea));\n      } else {\n        setTimeout(() => this._scrollToCaretPosition(textarea));\n      }\n    });\n    this._previousValue = value;\n    this._previousMinRows = this._minRows;\n  }\n  /**\n   * Resets the textarea to its original size\n   */\n  reset() {\n    // Do not try to change the textarea, if the initialHeight has not been determined yet\n    // This might potentially remove styles when reset() is called before ngAfterViewInit\n    if (this._initialHeight !== undefined) {\n      this._textareaElement.style.height = this._initialHeight;\n    }\n  }\n  _noopInputHandler() {\n    // no-op handler that ensures we're running change detection on input events.\n  }\n  /**\n   * Scrolls a textarea to the caret position. On Firefox resizing the textarea will\n   * prevent it from scrolling to the caret position. We need to re-set the selection\n   * in order for it to scroll to the proper position.\n   */\n  _scrollToCaretPosition(textarea) {\n    const {\n      selectionStart,\n      selectionEnd\n    } = textarea;\n    // IE will throw an \"Unspecified error\" if we try to set the selection range after the\n    // element has been removed from the DOM. Assert that the directive hasn't been destroyed\n    // between the time we requested the animation frame and when it was executed.\n    // Also note that we have to assert that the textarea is focused before we set the\n    // selection range. Setting the selection range on a non-focused textarea will cause\n    // it to receive focus on IE and Edge.\n    if (!this._destroyed.isStopped && this._hasFocus) {\n      textarea.setSelectionRange(selectionStart, selectionEnd);\n      textarea.scrollTop = this._cachedScrollTop;\n    }\n  }\n  static ɵfac = function CdkTextareaAutosize_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || CdkTextareaAutosize)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: CdkTextareaAutosize,\n    selectors: [[\"textarea\", \"cdkTextareaAutosize\", \"\"]],\n    hostAttrs: [\"rows\", \"1\", 1, \"cdk-textarea-autosize\"],\n    hostBindings: function CdkTextareaAutosize_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"input\", function CdkTextareaAutosize_input_HostBindingHandler() {\n          return ctx._noopInputHandler();\n        });\n      }\n    },\n    inputs: {\n      minRows: [0, \"cdkAutosizeMinRows\", \"minRows\"],\n      maxRows: [0, \"cdkAutosizeMaxRows\", \"maxRows\"],\n      enabled: [2, \"cdkTextareaAutosize\", \"enabled\", booleanAttribute],\n      placeholder: \"placeholder\"\n    },\n    exportAs: [\"cdkTextareaAutosize\"]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkTextareaAutosize, [{\n    type: Directive,\n    args: [{\n      selector: 'textarea[cdkTextareaAutosize]',\n      exportAs: 'cdkTextareaAutosize',\n      host: {\n        'class': 'cdk-textarea-autosize',\n        // Textarea elements that have the directive applied should have a single row by default.\n        // Browsers normally show two rows by default and therefore this limits the minRows binding.\n        'rows': '1',\n        '(input)': '_noopInputHandler()'\n      }\n    }]\n  }], () => [], {\n    minRows: [{\n      type: Input,\n      args: ['cdkAutosizeMinRows']\n    }],\n    maxRows: [{\n      type: Input,\n      args: ['cdkAutosizeMaxRows']\n    }],\n    enabled: [{\n      type: Input,\n      args: [{\n        alias: 'cdkTextareaAutosize',\n        transform: booleanAttribute\n      }]\n    }],\n    placeholder: [{\n      type: Input\n    }]\n  });\n})();\nclass TextFieldModule {\n  static ɵfac = function TextFieldModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || TextFieldModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: TextFieldModule,\n    imports: [CdkAutofill, CdkTextareaAutosize],\n    exports: [CdkAutofill, CdkTextareaAutosize]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TextFieldModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CdkAutofill, CdkTextareaAutosize],\n      exports: [CdkAutofill, CdkTextareaAutosize]\n    }]\n  }], null, null);\n})();\nexport { AutofillMonitor, CdkAutofill, CdkTextareaAutosize, TextFieldModule };\n", "import { coerceBooleanProperty } from '@angular/cdk/coercion';\nimport { Platform, getSupportedInputTypes } from '@angular/cdk/platform';\nimport { AutofillMonitor, TextFieldModule } from '@angular/cdk/text-field';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, inject, ElementRef, NgZone, Renderer2, isSignal, effect, booleanAttribute, Directive, Input, NgModule } from '@angular/core';\nimport { _IdGenerator } from '@angular/cdk/a11y';\nimport { NgControl, Validators, NgForm, FormGroupDirective } from '@angular/forms';\nimport { Subject } from 'rxjs';\nimport { M as MAT_INPUT_VALUE_ACCESSOR } from './input-value-accessor-4d18edb7.mjs';\nexport { M as MAT_INPUT_VALUE_ACCESSOR } from './input-value-accessor-4d18edb7.mjs';\nimport { M as MAT_FORM_FIELD, a as MatFormFieldControl } from './form-field-0a6b3078.mjs';\nexport { d as <PERSON><PERSON><PERSON><PERSON>, k as <PERSON><PERSON><PERSON><PERSON>ield, e as <PERSON><PERSON><PERSON>, b as <PERSON><PERSON><PERSON><PERSON>, g as Mat<PERSON>refix, i as MatSuffix } from './form-field-0a6b3078.mjs';\nimport { E as ErrorStateMatcher } from './error-options-b4cb6808.mjs';\nimport { _ as _ErrorStateTracker } from './error-state-66849a3f.mjs';\nimport { M as MatFormFieldModule } from './module-3bb03da5.mjs';\nimport { M as MatCommonModule } from './common-module-5a9c16bb.mjs';\nimport '@angular/cdk/bidi';\nimport '@angular/common';\nimport 'rxjs/operators';\nimport '@angular/cdk/observers/private';\nimport '@angular/cdk/observers';\n\n/** @docs-private */\nfunction getMatInputUnsupportedTypeError(type) {\n  return Error(`Input type \"${type}\" isn't supported by matInput.`);\n}\n\n// Invalid input type. Using one of these will throw an MatInputUnsupportedTypeError.\nconst MAT_INPUT_INVALID_TYPES = ['button', 'checkbox', 'file', 'hidden', 'image', 'radio', 'range', 'reset', 'submit'];\n/** Injection token that can be used to provide the default options for the input. */\nconst MAT_INPUT_CONFIG = new InjectionToken('MAT_INPUT_CONFIG');\nclass MatInput {\n  _elementRef = inject(ElementRef);\n  _platform = inject(Platform);\n  ngControl = inject(NgControl, {\n    optional: true,\n    self: true\n  });\n  _autofillMonitor = inject(AutofillMonitor);\n  _ngZone = inject(NgZone);\n  _formField = inject(MAT_FORM_FIELD, {\n    optional: true\n  });\n  _renderer = inject(Renderer2);\n  _uid = inject(_IdGenerator).getId('mat-input-');\n  _previousNativeValue;\n  _inputValueAccessor;\n  _signalBasedValueAccessor;\n  _previousPlaceholder;\n  _errorStateTracker;\n  _config = inject(MAT_INPUT_CONFIG, {\n    optional: true\n  });\n  _cleanupIosKeyup;\n  _cleanupWebkitWheel;\n  /** `aria-describedby` IDs assigned by the form field. */\n  _formFieldDescribedBy;\n  /** Whether the component is being rendered on the server. */\n  _isServer;\n  /** Whether the component is a native html select. */\n  _isNativeSelect;\n  /** Whether the component is a textarea. */\n  _isTextarea;\n  /** Whether the input is inside of a form field. */\n  _isInFormField;\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  focused = false;\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  stateChanges = new Subject();\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  controlType = 'mat-input';\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  autofilled = false;\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  get disabled() {\n    return this._disabled;\n  }\n  set disabled(value) {\n    this._disabled = coerceBooleanProperty(value);\n    // Browsers may not fire the blur event if the input is disabled too quickly.\n    // Reset from here to ensure that the element doesn't become stuck.\n    if (this.focused) {\n      this.focused = false;\n      this.stateChanges.next();\n    }\n  }\n  _disabled = false;\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  get id() {\n    return this._id;\n  }\n  set id(value) {\n    this._id = value || this._uid;\n  }\n  _id;\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  placeholder;\n  /**\n   * Name of the input.\n   * @docs-private\n   */\n  name;\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  get required() {\n    return this._required ?? this.ngControl?.control?.hasValidator(Validators.required) ?? false;\n  }\n  set required(value) {\n    this._required = coerceBooleanProperty(value);\n  }\n  _required;\n  /** Input type of the element. */\n  get type() {\n    return this._type;\n  }\n  set type(value) {\n    const prevType = this._type;\n    this._type = value || 'text';\n    this._validateType();\n    // When using Angular inputs, developers are no longer able to set the properties on the native\n    // input element. To ensure that bindings for `type` work, we need to sync the setter\n    // with the native property. Textarea elements don't support the type property or attribute.\n    if (!this._isTextarea && getSupportedInputTypes().has(this._type)) {\n      this._elementRef.nativeElement.type = this._type;\n    }\n    if (this._type !== prevType) {\n      this._ensureWheelDefaultBehavior();\n    }\n  }\n  _type = 'text';\n  /** An object used to control when error messages are shown. */\n  get errorStateMatcher() {\n    return this._errorStateTracker.matcher;\n  }\n  set errorStateMatcher(value) {\n    this._errorStateTracker.matcher = value;\n  }\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  userAriaDescribedBy;\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  get value() {\n    return this._signalBasedValueAccessor ? this._signalBasedValueAccessor.value() : this._inputValueAccessor.value;\n  }\n  set value(value) {\n    if (value !== this.value) {\n      if (this._signalBasedValueAccessor) {\n        this._signalBasedValueAccessor.value.set(value);\n      } else {\n        this._inputValueAccessor.value = value;\n      }\n      this.stateChanges.next();\n    }\n  }\n  /** Whether the element is readonly. */\n  get readonly() {\n    return this._readonly;\n  }\n  set readonly(value) {\n    this._readonly = coerceBooleanProperty(value);\n  }\n  _readonly = false;\n  /** Whether the input should remain interactive when it is disabled. */\n  disabledInteractive;\n  /** Whether the input is in an error state. */\n  get errorState() {\n    return this._errorStateTracker.errorState;\n  }\n  set errorState(value) {\n    this._errorStateTracker.errorState = value;\n  }\n  _neverEmptyInputTypes = ['date', 'datetime', 'datetime-local', 'month', 'time', 'week'].filter(t => getSupportedInputTypes().has(t));\n  constructor() {\n    const parentForm = inject(NgForm, {\n      optional: true\n    });\n    const parentFormGroup = inject(FormGroupDirective, {\n      optional: true\n    });\n    const defaultErrorStateMatcher = inject(ErrorStateMatcher);\n    const accessor = inject(MAT_INPUT_VALUE_ACCESSOR, {\n      optional: true,\n      self: true\n    });\n    const element = this._elementRef.nativeElement;\n    const nodeName = element.nodeName.toLowerCase();\n    if (accessor) {\n      if (isSignal(accessor.value)) {\n        this._signalBasedValueAccessor = accessor;\n      } else {\n        this._inputValueAccessor = accessor;\n      }\n    } else {\n      // If no input value accessor was explicitly specified, use the element as the input value\n      // accessor.\n      this._inputValueAccessor = element;\n    }\n    this._previousNativeValue = this.value;\n    // Force setter to be called in case id was not specified.\n    this.id = this.id;\n    // On some versions of iOS the caret gets stuck in the wrong place when holding down the delete\n    // key. In order to get around this we need to \"jiggle\" the caret loose. Since this bug only\n    // exists on iOS, we only bother to install the listener on iOS.\n    if (this._platform.IOS) {\n      this._ngZone.runOutsideAngular(() => {\n        this._cleanupIosKeyup = this._renderer.listen(element, 'keyup', this._iOSKeyupListener);\n      });\n    }\n    this._errorStateTracker = new _ErrorStateTracker(defaultErrorStateMatcher, this.ngControl, parentFormGroup, parentForm, this.stateChanges);\n    this._isServer = !this._platform.isBrowser;\n    this._isNativeSelect = nodeName === 'select';\n    this._isTextarea = nodeName === 'textarea';\n    this._isInFormField = !!this._formField;\n    this.disabledInteractive = this._config?.disabledInteractive || false;\n    if (this._isNativeSelect) {\n      this.controlType = element.multiple ? 'mat-native-select-multiple' : 'mat-native-select';\n    }\n    if (this._signalBasedValueAccessor) {\n      effect(() => {\n        // Read the value so the effect can register the dependency.\n        this._signalBasedValueAccessor.value();\n        this.stateChanges.next();\n      });\n    }\n  }\n  ngAfterViewInit() {\n    if (this._platform.isBrowser) {\n      this._autofillMonitor.monitor(this._elementRef.nativeElement).subscribe(event => {\n        this.autofilled = event.isAutofilled;\n        this.stateChanges.next();\n      });\n    }\n  }\n  ngOnChanges() {\n    this.stateChanges.next();\n  }\n  ngOnDestroy() {\n    this.stateChanges.complete();\n    if (this._platform.isBrowser) {\n      this._autofillMonitor.stopMonitoring(this._elementRef.nativeElement);\n    }\n    this._cleanupIosKeyup?.();\n    this._cleanupWebkitWheel?.();\n  }\n  ngDoCheck() {\n    if (this.ngControl) {\n      // We need to re-evaluate this on every change detection cycle, because there are some\n      // error triggers that we can't subscribe to (e.g. parent form submissions). This means\n      // that whatever logic is in here has to be super lean or we risk destroying the performance.\n      this.updateErrorState();\n      // Since the input isn't a `ControlValueAccessor`, we don't have a good way of knowing when\n      // the disabled state has changed. We can't use the `ngControl.statusChanges`, because it\n      // won't fire if the input is disabled with `emitEvents = false`, despite the input becoming\n      // disabled.\n      if (this.ngControl.disabled !== null && this.ngControl.disabled !== this.disabled) {\n        this.disabled = this.ngControl.disabled;\n        this.stateChanges.next();\n      }\n    }\n    // We need to dirty-check the native element's value, because there are some cases where\n    // we won't be notified when it changes (e.g. the consumer isn't using forms or they're\n    // updating the value using `emitEvent: false`).\n    this._dirtyCheckNativeValue();\n    // We need to dirty-check and set the placeholder attribute ourselves, because whether it's\n    // present or not depends on a query which is prone to \"changed after checked\" errors.\n    this._dirtyCheckPlaceholder();\n  }\n  /** Focuses the input. */\n  focus(options) {\n    this._elementRef.nativeElement.focus(options);\n  }\n  /** Refreshes the error state of the input. */\n  updateErrorState() {\n    this._errorStateTracker.updateErrorState();\n  }\n  /** Callback for the cases where the focused state of the input changes. */\n  _focusChanged(isFocused) {\n    if (isFocused === this.focused) {\n      return;\n    }\n    if (!this._isNativeSelect && isFocused && this.disabled && this.disabledInteractive) {\n      const element = this._elementRef.nativeElement;\n      // Focusing an input that has text will cause all the text to be selected. Clear it since\n      // the user won't be able to change it. This is based on the internal implementation.\n      if (element.type === 'number') {\n        // setSelectionRange doesn't work on number inputs so it needs to be set briefly to text.\n        element.type = 'text';\n        element.setSelectionRange(0, 0);\n        element.type = 'number';\n      } else {\n        element.setSelectionRange(0, 0);\n      }\n    }\n    this.focused = isFocused;\n    this.stateChanges.next();\n  }\n  _onInput() {\n    // This is a noop function and is used to let Angular know whenever the value changes.\n    // Angular will run a new change detection each time the `input` event has been dispatched.\n    // It's necessary that Angular recognizes the value change, because when floatingLabel\n    // is set to false and Angular forms aren't used, the placeholder won't recognize the\n    // value changes and will not disappear.\n    // Listening to the input event wouldn't be necessary when the input is using the\n    // FormsModule or ReactiveFormsModule, because Angular forms also listens to input events.\n  }\n  /** Does some manual dirty checking on the native input `value` property. */\n  _dirtyCheckNativeValue() {\n    const newValue = this._elementRef.nativeElement.value;\n    if (this._previousNativeValue !== newValue) {\n      this._previousNativeValue = newValue;\n      this.stateChanges.next();\n    }\n  }\n  /** Does some manual dirty checking on the native input `placeholder` attribute. */\n  _dirtyCheckPlaceholder() {\n    const placeholder = this._getPlaceholder();\n    if (placeholder !== this._previousPlaceholder) {\n      const element = this._elementRef.nativeElement;\n      this._previousPlaceholder = placeholder;\n      placeholder ? element.setAttribute('placeholder', placeholder) : element.removeAttribute('placeholder');\n    }\n  }\n  /** Gets the current placeholder of the form field. */\n  _getPlaceholder() {\n    return this.placeholder || null;\n  }\n  /** Make sure the input is a supported type. */\n  _validateType() {\n    if (MAT_INPUT_INVALID_TYPES.indexOf(this._type) > -1 && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getMatInputUnsupportedTypeError(this._type);\n    }\n  }\n  /** Checks whether the input type is one of the types that are never empty. */\n  _isNeverEmpty() {\n    return this._neverEmptyInputTypes.indexOf(this._type) > -1;\n  }\n  /** Checks whether the input is invalid based on the native validation. */\n  _isBadInput() {\n    // The `validity` property won't be present on platform-server.\n    let validity = this._elementRef.nativeElement.validity;\n    return validity && validity.badInput;\n  }\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  get empty() {\n    return !this._isNeverEmpty() && !this._elementRef.nativeElement.value && !this._isBadInput() && !this.autofilled;\n  }\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  get shouldLabelFloat() {\n    if (this._isNativeSelect) {\n      // For a single-selection `<select>`, the label should float when the selected option has\n      // a non-empty display value. For a `<select multiple>`, the label *always* floats to avoid\n      // overlapping the label with the options.\n      const selectElement = this._elementRef.nativeElement;\n      const firstOption = selectElement.options[0];\n      // On most browsers the `selectedIndex` will always be 0, however on IE and Edge it'll be\n      // -1 if the `value` is set to something, that isn't in the list of options, at a later point.\n      return this.focused || selectElement.multiple || !this.empty || !!(selectElement.selectedIndex > -1 && firstOption && firstOption.label);\n    } else {\n      return this.focused && !this.disabled || !this.empty;\n    }\n  }\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  setDescribedByIds(ids) {\n    const element = this._elementRef.nativeElement;\n    const existingDescribedBy = element.getAttribute('aria-describedby');\n    let toAssign;\n    // In some cases there might be some `aria-describedby` IDs that were assigned directly,\n    // like by the `AriaDescriber` (see #30011). Attempt to preserve them by taking the previous\n    // attribute value and filtering out the IDs that came from the previous `setDescribedByIds`\n    // call. Note the `|| ids` here allows us to avoid duplicating IDs on the first render.\n    if (existingDescribedBy) {\n      const exclude = this._formFieldDescribedBy || ids;\n      toAssign = ids.concat(existingDescribedBy.split(' ').filter(id => id && !exclude.includes(id)));\n    } else {\n      toAssign = ids;\n    }\n    this._formFieldDescribedBy = ids;\n    if (toAssign.length) {\n      element.setAttribute('aria-describedby', toAssign.join(' '));\n    } else {\n      element.removeAttribute('aria-describedby');\n    }\n  }\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  onContainerClick() {\n    // Do not re-focus the input element if the element is already focused. Otherwise it can happen\n    // that someone clicks on a time input and the cursor resets to the \"hours\" field while the\n    // \"minutes\" field was actually clicked. See: https://github.com/angular/components/issues/12849\n    if (!this.focused) {\n      this.focus();\n    }\n  }\n  /** Whether the form control is a native select that is displayed inline. */\n  _isInlineSelect() {\n    const element = this._elementRef.nativeElement;\n    return this._isNativeSelect && (element.multiple || element.size > 1);\n  }\n  _iOSKeyupListener = event => {\n    const el = event.target;\n    // Note: We specifically check for 0, rather than `!el.selectionStart`, because the two\n    // indicate different things. If the value is 0, it means that the caret is at the start\n    // of the input, whereas a value of `null` means that the input doesn't support\n    // manipulating the selection range. Inputs that don't support setting the selection range\n    // will throw an error so we want to avoid calling `setSelectionRange` on them. See:\n    // https://html.spec.whatwg.org/multipage/input.html#do-not-apply\n    if (!el.value && el.selectionStart === 0 && el.selectionEnd === 0) {\n      // Note: Just setting `0, 0` doesn't fix the issue. Setting\n      // `1, 1` fixes it for the first time that you type text and\n      // then hold delete. Toggling to `1, 1` and then back to\n      // `0, 0` seems to completely fix it.\n      el.setSelectionRange(1, 1);\n      el.setSelectionRange(0, 0);\n    }\n  };\n  _webkitBlinkWheelListener = () => {\n    // This is a noop function and is used to enable mouse wheel input\n    // on number inputs\n    // on blink and webkit browsers.\n  };\n  /**\n   * In blink and webkit browsers a focused number input does not increment or decrement its value\n   * on mouse wheel interaction unless a wheel event listener is attached to it or one of its\n   * ancestors or a passive wheel listener is attached somewhere in the DOM. For example: Hitting\n   * a tooltip once enables the mouse wheel input for all number inputs as long as it exists. In\n   * order to get reliable and intuitive behavior we apply a wheel event on our own thus making\n   * sure increment and decrement by mouse wheel works every time.\n   * @docs-private\n   */\n  _ensureWheelDefaultBehavior() {\n    this._cleanupWebkitWheel?.();\n    if (this._type === 'number' && (this._platform.BLINK || this._platform.WEBKIT)) {\n      this._cleanupWebkitWheel = this._renderer.listen(this._elementRef.nativeElement, 'wheel', this._webkitBlinkWheelListener);\n    }\n  }\n  /** Gets the value to set on the `readonly` attribute. */\n  _getReadonlyAttribute() {\n    if (this._isNativeSelect) {\n      return null;\n    }\n    if (this.readonly || this.disabled && this.disabledInteractive) {\n      return 'true';\n    }\n    return null;\n  }\n  static ɵfac = function MatInput_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatInput)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatInput,\n    selectors: [[\"input\", \"matInput\", \"\"], [\"textarea\", \"matInput\", \"\"], [\"select\", \"matNativeControl\", \"\"], [\"input\", \"matNativeControl\", \"\"], [\"textarea\", \"matNativeControl\", \"\"]],\n    hostAttrs: [1, \"mat-mdc-input-element\"],\n    hostVars: 21,\n    hostBindings: function MatInput_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"focus\", function MatInput_focus_HostBindingHandler() {\n          return ctx._focusChanged(true);\n        })(\"blur\", function MatInput_blur_HostBindingHandler() {\n          return ctx._focusChanged(false);\n        })(\"input\", function MatInput_input_HostBindingHandler() {\n          return ctx._onInput();\n        });\n      }\n      if (rf & 2) {\n        i0.ɵɵhostProperty(\"id\", ctx.id)(\"disabled\", ctx.disabled && !ctx.disabledInteractive)(\"required\", ctx.required);\n        i0.ɵɵattribute(\"name\", ctx.name || null)(\"readonly\", ctx._getReadonlyAttribute())(\"aria-disabled\", ctx.disabled && ctx.disabledInteractive ? \"true\" : null)(\"aria-invalid\", ctx.empty && ctx.required ? null : ctx.errorState)(\"aria-required\", ctx.required)(\"id\", ctx.id);\n        i0.ɵɵclassProp(\"mat-input-server\", ctx._isServer)(\"mat-mdc-form-field-textarea-control\", ctx._isInFormField && ctx._isTextarea)(\"mat-mdc-form-field-input-control\", ctx._isInFormField)(\"mat-mdc-input-disabled-interactive\", ctx.disabledInteractive)(\"mdc-text-field__input\", ctx._isInFormField)(\"mat-mdc-native-select-inline\", ctx._isInlineSelect());\n      }\n    },\n    inputs: {\n      disabled: \"disabled\",\n      id: \"id\",\n      placeholder: \"placeholder\",\n      name: \"name\",\n      required: \"required\",\n      type: \"type\",\n      errorStateMatcher: \"errorStateMatcher\",\n      userAriaDescribedBy: [0, \"aria-describedby\", \"userAriaDescribedBy\"],\n      value: \"value\",\n      readonly: \"readonly\",\n      disabledInteractive: [2, \"disabledInteractive\", \"disabledInteractive\", booleanAttribute]\n    },\n    exportAs: [\"matInput\"],\n    features: [i0.ɵɵProvidersFeature([{\n      provide: MatFormFieldControl,\n      useExisting: MatInput\n    }]), i0.ɵɵNgOnChangesFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatInput, [{\n    type: Directive,\n    args: [{\n      selector: `input[matInput], textarea[matInput], select[matNativeControl],\n      input[matNativeControl], textarea[matNativeControl]`,\n      exportAs: 'matInput',\n      host: {\n        'class': 'mat-mdc-input-element',\n        // The BaseMatInput parent class adds `mat-input-element`, `mat-form-field-control` and\n        // `mat-form-field-autofill-control` to the CSS class list, but this should not be added for\n        // this MDC equivalent input.\n        '[class.mat-input-server]': '_isServer',\n        '[class.mat-mdc-form-field-textarea-control]': '_isInFormField && _isTextarea',\n        '[class.mat-mdc-form-field-input-control]': '_isInFormField',\n        '[class.mat-mdc-input-disabled-interactive]': 'disabledInteractive',\n        '[class.mdc-text-field__input]': '_isInFormField',\n        '[class.mat-mdc-native-select-inline]': '_isInlineSelect()',\n        // Native input properties that are overwritten by Angular inputs need to be synced with\n        // the native input element. Otherwise property bindings for those don't work.\n        '[id]': 'id',\n        '[disabled]': 'disabled && !disabledInteractive',\n        '[required]': 'required',\n        '[attr.name]': 'name || null',\n        '[attr.readonly]': '_getReadonlyAttribute()',\n        '[attr.aria-disabled]': 'disabled && disabledInteractive ? \"true\" : null',\n        // Only mark the input as invalid for assistive technology if it has a value since the\n        // state usually overlaps with `aria-required` when the input is empty and can be redundant.\n        '[attr.aria-invalid]': '(empty && required) ? null : errorState',\n        '[attr.aria-required]': 'required',\n        // Native input properties that are overwritten by Angular inputs need to be synced with\n        // the native input element. Otherwise property bindings for those don't work.\n        '[attr.id]': 'id',\n        '(focus)': '_focusChanged(true)',\n        '(blur)': '_focusChanged(false)',\n        '(input)': '_onInput()'\n      },\n      providers: [{\n        provide: MatFormFieldControl,\n        useExisting: MatInput\n      }]\n    }]\n  }], () => [], {\n    disabled: [{\n      type: Input\n    }],\n    id: [{\n      type: Input\n    }],\n    placeholder: [{\n      type: Input\n    }],\n    name: [{\n      type: Input\n    }],\n    required: [{\n      type: Input\n    }],\n    type: [{\n      type: Input\n    }],\n    errorStateMatcher: [{\n      type: Input\n    }],\n    userAriaDescribedBy: [{\n      type: Input,\n      args: ['aria-describedby']\n    }],\n    value: [{\n      type: Input\n    }],\n    readonly: [{\n      type: Input\n    }],\n    disabledInteractive: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }]\n  });\n})();\nclass MatInputModule {\n  static ɵfac = function MatInputModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatInputModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: MatInputModule,\n    imports: [MatCommonModule, MatFormFieldModule, MatInput],\n    exports: [MatInput, MatFormFieldModule, TextFieldModule, MatCommonModule]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [MatCommonModule, MatFormFieldModule, MatFormFieldModule, TextFieldModule, MatCommonModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatInputModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatCommonModule, MatFormFieldModule, MatInput],\n      exports: [MatInput, MatFormFieldModule, TextFieldModule, MatCommonModule]\n    }]\n  }], null, null);\n})();\nexport { MAT_INPUT_CONFIG, MatInput, MatInputModule, getMatInputUnsupportedTypeError };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,kBAA+B;AAM/B,uBAA0B;AAG1B,IAAM,2BAAN,MAAM,0BAAyB;AAAA,EAC7B,OAAO,OAAO,SAAS,iCAAiC,mBAAmB;AACzE,WAAO,KAAK,qBAAqB,2BAA0B;AAAA,EAC7D;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,cAAc,CAAC;AAAA,IAC5B,WAAW,CAAC,+BAA+B,EAAE;AAAA,IAC7C,OAAO;AAAA,IACP,MAAM;AAAA,IACN,UAAU,SAAS,kCAAkC,IAAI,KAAK;AAAA,IAAC;AAAA,IAC/D,QAAQ,CAAC,ymBAAymB;AAAA,IAClnB,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,0BAA0B,CAAC;AAAA,IACjG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,kBAAkB;AAAA,MACjC,MAAM;AAAA,QACJ,+BAA+B;AAAA,MACjC;AAAA,MACA,QAAQ,CAAC,ymBAAymB;AAAA,IACpnB,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAGH,IAAM,kBAAkB;AAAA,EACtB,SAAS;AACX;AAMA,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACpB,YAAY,OAAO,QAAQ;AAAA,EAC3B,UAAU,OAAO,MAAM;AAAA,EACvB,YAAY,OAAO,gBAAgB,EAAE,eAAe,MAAM,IAAI;AAAA,EAC9D,eAAe,OAAO,sBAAsB;AAAA,EAC5C,qBAAqB,oBAAI,IAAI;AAAA,EAC7B,cAAc;AAAA,EAAC;AAAA,EACf,QAAQ,cAAc;AACpB,QAAI,CAAC,KAAK,UAAU,WAAW;AAC7B,aAAO;AAAA,IACT;AACA,SAAK,aAAa,KAAK,wBAAwB;AAC/C,UAAM,UAAU,cAAc,YAAY;AAC1C,UAAM,OAAO,KAAK,mBAAmB,IAAI,OAAO;AAChD,QAAI,MAAM;AACR,aAAO,KAAK;AAAA,IACd;AACA,UAAM,UAAU,IAAI,oBAAQ;AAC5B,UAAM,WAAW;AACjB,UAAM,WAAW,WAAS;AAIxB,UAAI,MAAM,kBAAkB,mCAAmC,CAAC,QAAQ,UAAU,SAAS,QAAQ,GAAG;AACpG,gBAAQ,UAAU,IAAI,QAAQ;AAC9B,aAAK,QAAQ,IAAI,MAAM,QAAQ,KAAK;AAAA,UAClC,QAAQ,MAAM;AAAA,UACd,cAAc;AAAA,QAChB,CAAC,CAAC;AAAA,MACJ,WAAW,MAAM,kBAAkB,iCAAiC,QAAQ,UAAU,SAAS,QAAQ,GAAG;AACxG,gBAAQ,UAAU,OAAO,QAAQ;AACjC,aAAK,QAAQ,IAAI,MAAM,QAAQ,KAAK;AAAA,UAClC,QAAQ,MAAM;AAAA,UACd,cAAc;AAAA,QAChB,CAAC,CAAC;AAAA,MACJ;AAAA,IACF;AACA,UAAM,WAAW,KAAK,QAAQ,kBAAkB,MAAM;AACpD,cAAQ,UAAU,IAAI,mCAAmC;AACzD,aAAO,sBAAsB,KAAK,WAAW,SAAS,kBAAkB,UAAU,eAAe;AAAA,IACnG,CAAC;AACD,SAAK,mBAAmB,IAAI,SAAS;AAAA,MACnC;AAAA,MACA;AAAA,IACF,CAAC;AACD,WAAO;AAAA,EACT;AAAA,EACA,eAAe,cAAc;AAC3B,UAAM,UAAU,cAAc,YAAY;AAC1C,UAAM,OAAO,KAAK,mBAAmB,IAAI,OAAO;AAChD,QAAI,MAAM;AACR,WAAK,SAAS;AACd,WAAK,QAAQ,SAAS;AACtB,cAAQ,UAAU,OAAO,mCAAmC;AAC5D,cAAQ,UAAU,OAAO,2BAA2B;AACpD,WAAK,mBAAmB,OAAO,OAAO;AAAA,IACxC;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,mBAAmB,QAAQ,CAAC,OAAO,YAAY,KAAK,eAAe,OAAO,CAAC;AAAA,EAClF;AAAA,EACA,OAAO,OAAO,SAAS,wBAAwB,mBAAmB;AAChE,WAAO,KAAK,qBAAqB,kBAAiB;AAAA,EACpD;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,iBAAgB;AAAA,IACzB,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AAEH,IAAM,cAAN,MAAM,aAAY;AAAA,EAChB,cAAc,OAAO,UAAU;AAAA,EAC/B,mBAAmB,OAAO,eAAe;AAAA;AAAA,EAEzC,cAAc,IAAI,aAAa;AAAA,EAC/B,cAAc;AAAA,EAAC;AAAA,EACf,WAAW;AACT,SAAK,iBAAiB,QAAQ,KAAK,WAAW,EAAE,UAAU,WAAS,KAAK,YAAY,KAAK,KAAK,CAAC;AAAA,EACjG;AAAA,EACA,cAAc;AACZ,SAAK,iBAAiB,eAAe,KAAK,WAAW;AAAA,EACvD;AAAA,EACA,OAAO,OAAO,SAAS,oBAAoB,mBAAmB;AAC5D,WAAO,KAAK,qBAAqB,cAAa;AAAA,EAChD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,eAAe,EAAE,CAAC;AAAA,IACnC,SAAS;AAAA,MACP,aAAa;AAAA,IACf;AAAA,EACF,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,aAAa,CAAC;AAAA,IACpF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAGH,IAAM,sBAAN,MAAM,qBAAoB;AAAA,EACxB,cAAc,OAAO,UAAU;AAAA,EAC/B,YAAY,OAAO,QAAQ;AAAA,EAC3B,UAAU,OAAO,MAAM;AAAA,EACvB,YAAY,OAAO,SAAS;AAAA,EAC5B,gBAAgB,IAAI,oBAAQ;AAAA;AAAA,EAE5B;AAAA,EACA;AAAA,EACA,aAAa,IAAI,oBAAQ;AAAA,EACzB;AAAA,EACA;AAAA,EACA;AAAA,EACA,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMX,mBAAmB;AAAA,EACnB;AAAA;AAAA,EAEA,IAAI,UAAU;AACZ,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,QAAQ,OAAO;AACjB,SAAK,WAAW,qBAAqB,KAAK;AAC1C,SAAK,cAAc;AAAA,EACrB;AAAA;AAAA,EAEA,IAAI,UAAU;AACZ,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,QAAQ,OAAO;AACjB,SAAK,WAAW,qBAAqB,KAAK;AAC1C,SAAK,cAAc;AAAA,EACrB;AAAA;AAAA,EAEA,IAAI,UAAU;AACZ,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,QAAQ,OAAO;AAGjB,QAAI,KAAK,aAAa,OAAO;AAC3B,OAAC,KAAK,WAAW,SAAS,KAAK,mBAAmB,IAAI,IAAI,KAAK,MAAM;AAAA,IACvE;AAAA,EACF;AAAA,EACA,IAAI,cAAc;AAChB,WAAO,KAAK,iBAAiB;AAAA,EAC/B;AAAA,EACA,IAAI,YAAY,OAAO;AACrB,SAAK,2BAA2B;AAChC,QAAI,OAAO;AACT,WAAK,iBAAiB,aAAa,eAAe,KAAK;AAAA,IACzD,OAAO;AACL,WAAK,iBAAiB,gBAAgB,aAAa;AAAA,IACrD;AACA,SAAK,gCAAgC;AAAA,EACvC;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA,YAAY,OAAO,UAAU;AAAA,IAC3B,UAAU;AAAA,EACZ,CAAC;AAAA,EACD;AAAA,EACA,gBAAgB;AAAA,EAChB,cAAc;AACZ,UAAM,cAAc,OAAO,sBAAsB;AACjD,gBAAY,KAAK,wBAAwB;AACzC,SAAK,mBAAmB,KAAK,YAAY;AAAA,EAC3C;AAAA;AAAA,EAEA,gBAAgB;AACd,UAAM,YAAY,KAAK,WAAW,KAAK,oBAAoB,GAAG,KAAK,UAAU,KAAK,iBAAiB,OAAO;AAC1G,QAAI,WAAW;AACb,WAAK,iBAAiB,MAAM,YAAY;AAAA,IAC1C;AAAA,EACF;AAAA;AAAA,EAEA,gBAAgB;AACd,UAAM,YAAY,KAAK,WAAW,KAAK,oBAAoB,GAAG,KAAK,UAAU,KAAK,iBAAiB,OAAO;AAC1G,QAAI,WAAW;AACb,WAAK,iBAAiB,MAAM,YAAY;AAAA,IAC1C;AAAA,EACF;AAAA,EACA,kBAAkB;AAChB,QAAI,KAAK,UAAU,WAAW;AAE5B,WAAK,iBAAiB,KAAK,iBAAiB,MAAM;AAClD,WAAK,mBAAmB;AACxB,WAAK,QAAQ,kBAAkB,MAAM;AACnC,aAAK,oBAAoB,CAAC,KAAK,UAAU,OAAO,UAAU,UAAU,MAAM,KAAK,cAAc,KAAK,CAAC,GAAG,KAAK,UAAU,OAAO,KAAK,kBAAkB,SAAS,KAAK,iBAAiB,GAAG,KAAK,UAAU,OAAO,KAAK,kBAAkB,QAAQ,KAAK,iBAAiB,CAAC;AACjQ,aAAK,cAAc,SAAK,4BAAU,EAAE,CAAC,EAAE,UAAU,MAAM;AAGrD,eAAK,oBAAoB,KAAK,2BAA2B;AACzD,eAAK,mBAAmB,IAAI;AAAA,QAC9B,CAAC;AAAA,MACH,CAAC;AACD,WAAK,gBAAgB;AACrB,WAAK,mBAAmB,IAAI;AAAA,IAC9B;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,mBAAmB,QAAQ,aAAW,QAAQ,CAAC;AACpD,SAAK,cAAc,SAAS;AAC5B,SAAK,WAAW,KAAK;AACrB,SAAK,WAAW,SAAS;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,2BAA2B;AACzB,QAAI,KAAK,mBAAmB;AAC1B;AAAA,IACF;AAEA,UAAM,gBAAgB,KAAK,iBAAiB,UAAU,KAAK;AAC3D,UAAM,cAAc,cAAc;AAClC,kBAAc,OAAO;AAIrB,gBAAY,WAAW;AACvB,gBAAY,aAAa;AACzB,gBAAY,SAAS;AACrB,gBAAY,UAAU;AACtB,gBAAY,SAAS;AACrB,gBAAY,YAAY;AACxB,gBAAY,YAAY;AAExB,gBAAY,MAAM,YAAY,SAAS,YAAY,OAAO,YAAY,QAAQ;AAM9E,gBAAY,WAAW;AACvB,SAAK,iBAAiB,WAAW,YAAY,aAAa;AAC1D,SAAK,oBAAoB,cAAc;AACvC,kBAAc,OAAO;AAErB,SAAK,cAAc;AACnB,SAAK,cAAc;AAAA,EACrB;AAAA,EACA,uBAAuB;AACrB,UAAM,UAAU,KAAK;AACrB,UAAM,iBAAiB,QAAQ,MAAM,gBAAgB;AACrD,UAAM,YAAY,KAAK,UAAU;AACjC,UAAM,oBAAoB,aAAa,KAAK;AAC5C,UAAM,iBAAiB,YAAY,4CAA4C;AAI/E,QAAI,mBAAmB;AACrB,cAAQ,MAAM,eAAe,GAAG,QAAQ,YAAY;AAAA,IACtD;AAGA,YAAQ,UAAU,IAAI,cAAc;AAGpC,UAAM,eAAe,QAAQ,eAAe;AAC5C,YAAQ,UAAU,OAAO,cAAc;AACvC,QAAI,mBAAmB;AACrB,cAAQ,MAAM,eAAe;AAAA,IAC/B;AACA,WAAO;AAAA,EACT;AAAA,EACA,kCAAkC;AAChC,QAAI,CAAC,KAAK,iBAAiB,KAAK,4BAA4B,QAAW;AACrE;AAAA,IACF;AACA,QAAI,CAAC,KAAK,aAAa;AACrB,WAAK,2BAA2B;AAChC;AAAA,IACF;AACA,UAAM,QAAQ,KAAK,iBAAiB;AACpC,SAAK,iBAAiB,QAAQ,KAAK,iBAAiB;AACpD,SAAK,2BAA2B,KAAK,qBAAqB;AAC1D,SAAK,iBAAiB,QAAQ;AAAA,EAChC;AAAA;AAAA,EAEA,oBAAoB,WAAS;AAC3B,SAAK,YAAY,MAAM,SAAS;AAAA,EAClC;AAAA,EACA,YAAY;AACV,QAAI,KAAK,UAAU,WAAW;AAC5B,WAAK,mBAAmB;AAAA,IAC1B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,mBAAmB,QAAQ,OAAO;AAEhC,QAAI,CAAC,KAAK,UAAU;AAClB;AAAA,IACF;AACA,SAAK,yBAAyB;AAC9B,SAAK,gCAAgC;AACrC,SAAK,mBAAmB,KAAK,iBAAiB;AAG9C,QAAI,CAAC,KAAK,mBAAmB;AAC3B;AAAA,IACF;AACA,UAAM,WAAW,KAAK,YAAY;AAClC,UAAM,QAAQ,SAAS;AAEvB,QAAI,CAAC,SAAS,KAAK,aAAa,KAAK,oBAAoB,UAAU,KAAK,gBAAgB;AACtF;AAAA,IACF;AACA,UAAM,eAAe,KAAK,qBAAqB;AAC/C,UAAM,SAAS,KAAK,IAAI,cAAc,KAAK,4BAA4B,CAAC;AAExE,aAAS,MAAM,SAAS,GAAG,MAAM;AACjC,SAAK,QAAQ,kBAAkB,MAAM;AACnC,UAAI,OAAO,0BAA0B,aAAa;AAChD,8BAAsB,MAAM,KAAK,uBAAuB,QAAQ,CAAC;AAAA,MACnE,OAAO;AACL,mBAAW,MAAM,KAAK,uBAAuB,QAAQ,CAAC;AAAA,MACxD;AAAA,IACF,CAAC;AACD,SAAK,iBAAiB;AACtB,SAAK,mBAAmB,KAAK;AAAA,EAC/B;AAAA;AAAA;AAAA;AAAA,EAIA,QAAQ;AAGN,QAAI,KAAK,mBAAmB,QAAW;AACrC,WAAK,iBAAiB,MAAM,SAAS,KAAK;AAAA,IAC5C;AAAA,EACF;AAAA,EACA,oBAAoB;AAAA,EAEpB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,uBAAuB,UAAU;AAC/B,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AAOJ,QAAI,CAAC,KAAK,WAAW,aAAa,KAAK,WAAW;AAChD,eAAS,kBAAkB,gBAAgB,YAAY;AACvD,eAAS,YAAY,KAAK;AAAA,IAC5B;AAAA,EACF;AAAA,EACA,OAAO,OAAO,SAAS,4BAA4B,mBAAmB;AACpE,WAAO,KAAK,qBAAqB,sBAAqB;AAAA,EACxD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,YAAY,uBAAuB,EAAE,CAAC;AAAA,IACnD,WAAW,CAAC,QAAQ,KAAK,GAAG,uBAAuB;AAAA,IACnD,cAAc,SAAS,iCAAiC,IAAI,KAAK;AAC/D,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,SAAS,SAAS,+CAA+C;AAC7E,iBAAO,IAAI,kBAAkB;AAAA,QAC/B,CAAC;AAAA,MACH;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,SAAS,CAAC,GAAG,sBAAsB,SAAS;AAAA,MAC5C,SAAS,CAAC,GAAG,sBAAsB,SAAS;AAAA,MAC5C,SAAS,CAAC,GAAG,uBAAuB,WAAW,gBAAgB;AAAA,MAC/D,aAAa;AAAA,IACf;AAAA,IACA,UAAU,CAAC,qBAAqB;AAAA,EAClC,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,qBAAqB,CAAC;AAAA,IAC5F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,SAAS;AAAA;AAAA;AAAA,QAGT,QAAQ;AAAA,QACR,WAAW;AAAA,MACb;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC,oBAAoB;AAAA,IAC7B,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC,oBAAoB;AAAA,IAC7B,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,OAAO;AAAA,QACP,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACpB,OAAO,OAAO,SAAS,wBAAwB,mBAAmB;AAChE,WAAO,KAAK,qBAAqB,kBAAiB;AAAA,EACpD;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,aAAa,mBAAmB;AAAA,IAC1C,SAAS,CAAC,aAAa,mBAAmB;AAAA,EAC5C,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB,CAAC,CAAC;AACrD;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,aAAa,mBAAmB;AAAA,MAC1C,SAAS,CAAC,aAAa,mBAAmB;AAAA,IAC5C,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;;;AC9fH,IAAAA,eAAwB;AAWxB,IAAAC,oBAAO;AAKP,SAAS,gCAAgC,MAAM;AAC7C,SAAO,MAAM,eAAe,IAAI,gCAAgC;AAClE;AAGA,IAAM,0BAA0B,CAAC,UAAU,YAAY,QAAQ,UAAU,SAAS,SAAS,SAAS,SAAS,QAAQ;AAErH,IAAM,mBAAmB,IAAI,eAAe,kBAAkB;AAC9D,IAAM,WAAN,MAAM,UAAS;AAAA,EACb,cAAc,OAAO,UAAU;AAAA,EAC/B,YAAY,OAAO,QAAQ;AAAA,EAC3B,YAAY,OAAO,WAAW;AAAA,IAC5B,UAAU;AAAA,IACV,MAAM;AAAA,EACR,CAAC;AAAA,EACD,mBAAmB,OAAO,eAAe;AAAA,EACzC,UAAU,OAAO,MAAM;AAAA,EACvB,aAAa,OAAO,gBAAgB;AAAA,IAClC,UAAU;AAAA,EACZ,CAAC;AAAA,EACD,YAAY,OAAO,SAAS;AAAA,EAC5B,OAAO,OAAO,YAAY,EAAE,MAAM,YAAY;AAAA,EAC9C;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,UAAU,OAAO,kBAAkB;AAAA,IACjC,UAAU;AAAA,EACZ,CAAC;AAAA,EACD;AAAA,EACA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKV,eAAe,IAAI,qBAAQ;AAAA;AAAA;AAAA;AAAA;AAAA,EAK3B,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA,EAKd,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA,EAKb,IAAI,WAAW;AACb,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,SAAS,OAAO;AAClB,SAAK,YAAY,sBAAsB,KAAK;AAG5C,QAAI,KAAK,SAAS;AAChB,WAAK,UAAU;AACf,WAAK,aAAa,KAAK;AAAA,IACzB;AAAA,EACF;AAAA,EACA,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA,EAKZ,IAAI,KAAK;AACP,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,GAAG,OAAO;AACZ,SAAK,MAAM,SAAS,KAAK;AAAA,EAC3B;AAAA,EACA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,WAAW;AACb,WAAO,KAAK,aAAa,KAAK,WAAW,SAAS,aAAa,WAAW,QAAQ,KAAK;AAAA,EACzF;AAAA,EACA,IAAI,SAAS,OAAO;AAClB,SAAK,YAAY,sBAAsB,KAAK;AAAA,EAC9C;AAAA,EACA;AAAA;AAAA,EAEA,IAAI,OAAO;AACT,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,KAAK,OAAO;AACd,UAAM,WAAW,KAAK;AACtB,SAAK,QAAQ,SAAS;AACtB,SAAK,cAAc;AAInB,QAAI,CAAC,KAAK,eAAe,uBAAuB,EAAE,IAAI,KAAK,KAAK,GAAG;AACjE,WAAK,YAAY,cAAc,OAAO,KAAK;AAAA,IAC7C;AACA,QAAI,KAAK,UAAU,UAAU;AAC3B,WAAK,4BAA4B;AAAA,IACnC;AAAA,EACF;AAAA,EACA,QAAQ;AAAA;AAAA,EAER,IAAI,oBAAoB;AACtB,WAAO,KAAK,mBAAmB;AAAA,EACjC;AAAA,EACA,IAAI,kBAAkB,OAAO;AAC3B,SAAK,mBAAmB,UAAU;AAAA,EACpC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,QAAQ;AACV,WAAO,KAAK,4BAA4B,KAAK,0BAA0B,MAAM,IAAI,KAAK,oBAAoB;AAAA,EAC5G;AAAA,EACA,IAAI,MAAM,OAAO;AACf,QAAI,UAAU,KAAK,OAAO;AACxB,UAAI,KAAK,2BAA2B;AAClC,aAAK,0BAA0B,MAAM,IAAI,KAAK;AAAA,MAChD,OAAO;AACL,aAAK,oBAAoB,QAAQ;AAAA,MACnC;AACA,WAAK,aAAa,KAAK;AAAA,IACzB;AAAA,EACF;AAAA;AAAA,EAEA,IAAI,WAAW;AACb,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,SAAS,OAAO;AAClB,SAAK,YAAY,sBAAsB,KAAK;AAAA,EAC9C;AAAA,EACA,YAAY;AAAA;AAAA,EAEZ;AAAA;AAAA,EAEA,IAAI,aAAa;AACf,WAAO,KAAK,mBAAmB;AAAA,EACjC;AAAA,EACA,IAAI,WAAW,OAAO;AACpB,SAAK,mBAAmB,aAAa;AAAA,EACvC;AAAA,EACA,wBAAwB,CAAC,QAAQ,YAAY,kBAAkB,SAAS,QAAQ,MAAM,EAAE,OAAO,OAAK,uBAAuB,EAAE,IAAI,CAAC,CAAC;AAAA,EACnI,cAAc;AACZ,UAAM,aAAa,OAAO,QAAQ;AAAA,MAChC,UAAU;AAAA,IACZ,CAAC;AACD,UAAM,kBAAkB,OAAO,oBAAoB;AAAA,MACjD,UAAU;AAAA,IACZ,CAAC;AACD,UAAM,2BAA2B,OAAO,iBAAiB;AACzD,UAAM,WAAW,OAAO,0BAA0B;AAAA,MAChD,UAAU;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AACD,UAAM,UAAU,KAAK,YAAY;AACjC,UAAM,WAAW,QAAQ,SAAS,YAAY;AAC9C,QAAI,UAAU;AACZ,UAAI,SAAS,SAAS,KAAK,GAAG;AAC5B,aAAK,4BAA4B;AAAA,MACnC,OAAO;AACL,aAAK,sBAAsB;AAAA,MAC7B;AAAA,IACF,OAAO;AAGL,WAAK,sBAAsB;AAAA,IAC7B;AACA,SAAK,uBAAuB,KAAK;AAEjC,SAAK,KAAK,KAAK;AAIf,QAAI,KAAK,UAAU,KAAK;AACtB,WAAK,QAAQ,kBAAkB,MAAM;AACnC,aAAK,mBAAmB,KAAK,UAAU,OAAO,SAAS,SAAS,KAAK,iBAAiB;AAAA,MACxF,CAAC;AAAA,IACH;AACA,SAAK,qBAAqB,IAAI,mBAAmB,0BAA0B,KAAK,WAAW,iBAAiB,YAAY,KAAK,YAAY;AACzI,SAAK,YAAY,CAAC,KAAK,UAAU;AACjC,SAAK,kBAAkB,aAAa;AACpC,SAAK,cAAc,aAAa;AAChC,SAAK,iBAAiB,CAAC,CAAC,KAAK;AAC7B,SAAK,sBAAsB,KAAK,SAAS,uBAAuB;AAChE,QAAI,KAAK,iBAAiB;AACxB,WAAK,cAAc,QAAQ,WAAW,+BAA+B;AAAA,IACvE;AACA,QAAI,KAAK,2BAA2B;AAClC,aAAO,MAAM;AAEX,aAAK,0BAA0B,MAAM;AACrC,aAAK,aAAa,KAAK;AAAA,MACzB,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,kBAAkB;AAChB,QAAI,KAAK,UAAU,WAAW;AAC5B,WAAK,iBAAiB,QAAQ,KAAK,YAAY,aAAa,EAAE,UAAU,WAAS;AAC/E,aAAK,aAAa,MAAM;AACxB,aAAK,aAAa,KAAK;AAAA,MACzB,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,aAAa,KAAK;AAAA,EACzB;AAAA,EACA,cAAc;AACZ,SAAK,aAAa,SAAS;AAC3B,QAAI,KAAK,UAAU,WAAW;AAC5B,WAAK,iBAAiB,eAAe,KAAK,YAAY,aAAa;AAAA,IACrE;AACA,SAAK,mBAAmB;AACxB,SAAK,sBAAsB;AAAA,EAC7B;AAAA,EACA,YAAY;AACV,QAAI,KAAK,WAAW;AAIlB,WAAK,iBAAiB;AAKtB,UAAI,KAAK,UAAU,aAAa,QAAQ,KAAK,UAAU,aAAa,KAAK,UAAU;AACjF,aAAK,WAAW,KAAK,UAAU;AAC/B,aAAK,aAAa,KAAK;AAAA,MACzB;AAAA,IACF;AAIA,SAAK,uBAAuB;AAG5B,SAAK,uBAAuB;AAAA,EAC9B;AAAA;AAAA,EAEA,MAAM,SAAS;AACb,SAAK,YAAY,cAAc,MAAM,OAAO;AAAA,EAC9C;AAAA;AAAA,EAEA,mBAAmB;AACjB,SAAK,mBAAmB,iBAAiB;AAAA,EAC3C;AAAA;AAAA,EAEA,cAAc,WAAW;AACvB,QAAI,cAAc,KAAK,SAAS;AAC9B;AAAA,IACF;AACA,QAAI,CAAC,KAAK,mBAAmB,aAAa,KAAK,YAAY,KAAK,qBAAqB;AACnF,YAAM,UAAU,KAAK,YAAY;AAGjC,UAAI,QAAQ,SAAS,UAAU;AAE7B,gBAAQ,OAAO;AACf,gBAAQ,kBAAkB,GAAG,CAAC;AAC9B,gBAAQ,OAAO;AAAA,MACjB,OAAO;AACL,gBAAQ,kBAAkB,GAAG,CAAC;AAAA,MAChC;AAAA,IACF;AACA,SAAK,UAAU;AACf,SAAK,aAAa,KAAK;AAAA,EACzB;AAAA,EACA,WAAW;AAAA,EAQX;AAAA;AAAA,EAEA,yBAAyB;AACvB,UAAM,WAAW,KAAK,YAAY,cAAc;AAChD,QAAI,KAAK,yBAAyB,UAAU;AAC1C,WAAK,uBAAuB;AAC5B,WAAK,aAAa,KAAK;AAAA,IACzB;AAAA,EACF;AAAA;AAAA,EAEA,yBAAyB;AACvB,UAAM,cAAc,KAAK,gBAAgB;AACzC,QAAI,gBAAgB,KAAK,sBAAsB;AAC7C,YAAM,UAAU,KAAK,YAAY;AACjC,WAAK,uBAAuB;AAC5B,oBAAc,QAAQ,aAAa,eAAe,WAAW,IAAI,QAAQ,gBAAgB,aAAa;AAAA,IACxG;AAAA,EACF;AAAA;AAAA,EAEA,kBAAkB;AAChB,WAAO,KAAK,eAAe;AAAA,EAC7B;AAAA;AAAA,EAEA,gBAAgB;AACd,QAAI,wBAAwB,QAAQ,KAAK,KAAK,IAAI,OAAO,OAAO,cAAc,eAAe,YAAY;AACvG,YAAM,gCAAgC,KAAK,KAAK;AAAA,IAClD;AAAA,EACF;AAAA;AAAA,EAEA,gBAAgB;AACd,WAAO,KAAK,sBAAsB,QAAQ,KAAK,KAAK,IAAI;AAAA,EAC1D;AAAA;AAAA,EAEA,cAAc;AAEZ,QAAI,WAAW,KAAK,YAAY,cAAc;AAC9C,WAAO,YAAY,SAAS;AAAA,EAC9B;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,QAAQ;AACV,WAAO,CAAC,KAAK,cAAc,KAAK,CAAC,KAAK,YAAY,cAAc,SAAS,CAAC,KAAK,YAAY,KAAK,CAAC,KAAK;AAAA,EACxG;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,mBAAmB;AACrB,QAAI,KAAK,iBAAiB;AAIxB,YAAM,gBAAgB,KAAK,YAAY;AACvC,YAAM,cAAc,cAAc,QAAQ,CAAC;AAG3C,aAAO,KAAK,WAAW,cAAc,YAAY,CAAC,KAAK,SAAS,CAAC,EAAE,cAAc,gBAAgB,MAAM,eAAe,YAAY;AAAA,IACpI,OAAO;AACL,aAAO,KAAK,WAAW,CAAC,KAAK,YAAY,CAAC,KAAK;AAAA,IACjD;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,kBAAkB,KAAK;AACrB,UAAM,UAAU,KAAK,YAAY;AACjC,UAAM,sBAAsB,QAAQ,aAAa,kBAAkB;AACnE,QAAI;AAKJ,QAAI,qBAAqB;AACvB,YAAM,UAAU,KAAK,yBAAyB;AAC9C,iBAAW,IAAI,OAAO,oBAAoB,MAAM,GAAG,EAAE,OAAO,QAAM,MAAM,CAAC,QAAQ,SAAS,EAAE,CAAC,CAAC;AAAA,IAChG,OAAO;AACL,iBAAW;AAAA,IACb;AACA,SAAK,wBAAwB;AAC7B,QAAI,SAAS,QAAQ;AACnB,cAAQ,aAAa,oBAAoB,SAAS,KAAK,GAAG,CAAC;AAAA,IAC7D,OAAO;AACL,cAAQ,gBAAgB,kBAAkB;AAAA,IAC5C;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,mBAAmB;AAIjB,QAAI,CAAC,KAAK,SAAS;AACjB,WAAK,MAAM;AAAA,IACb;AAAA,EACF;AAAA;AAAA,EAEA,kBAAkB;AAChB,UAAM,UAAU,KAAK,YAAY;AACjC,WAAO,KAAK,oBAAoB,QAAQ,YAAY,QAAQ,OAAO;AAAA,EACrE;AAAA,EACA,oBAAoB,WAAS;AAC3B,UAAM,KAAK,MAAM;AAOjB,QAAI,CAAC,GAAG,SAAS,GAAG,mBAAmB,KAAK,GAAG,iBAAiB,GAAG;AAKjE,SAAG,kBAAkB,GAAG,CAAC;AACzB,SAAG,kBAAkB,GAAG,CAAC;AAAA,IAC3B;AAAA,EACF;AAAA,EACA,4BAA4B,MAAM;AAAA,EAIlC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,8BAA8B;AAC5B,SAAK,sBAAsB;AAC3B,QAAI,KAAK,UAAU,aAAa,KAAK,UAAU,SAAS,KAAK,UAAU,SAAS;AAC9E,WAAK,sBAAsB,KAAK,UAAU,OAAO,KAAK,YAAY,eAAe,SAAS,KAAK,yBAAyB;AAAA,IAC1H;AAAA,EACF;AAAA;AAAA,EAEA,wBAAwB;AACtB,QAAI,KAAK,iBAAiB;AACxB,aAAO;AAAA,IACT;AACA,QAAI,KAAK,YAAY,KAAK,YAAY,KAAK,qBAAqB;AAC9D,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA,EACA,OAAO,OAAO,SAAS,iBAAiB,mBAAmB;AACzD,WAAO,KAAK,qBAAqB,WAAU;AAAA,EAC7C;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,SAAS,YAAY,EAAE,GAAG,CAAC,YAAY,YAAY,EAAE,GAAG,CAAC,UAAU,oBAAoB,EAAE,GAAG,CAAC,SAAS,oBAAoB,EAAE,GAAG,CAAC,YAAY,oBAAoB,EAAE,CAAC;AAAA,IAChL,WAAW,CAAC,GAAG,uBAAuB;AAAA,IACtC,UAAU;AAAA,IACV,cAAc,SAAS,sBAAsB,IAAI,KAAK;AACpD,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,SAAS,SAAS,oCAAoC;AAClE,iBAAO,IAAI,cAAc,IAAI;AAAA,QAC/B,CAAC,EAAE,QAAQ,SAAS,mCAAmC;AACrD,iBAAO,IAAI,cAAc,KAAK;AAAA,QAChC,CAAC,EAAE,SAAS,SAAS,oCAAoC;AACvD,iBAAO,IAAI,SAAS;AAAA,QACtB,CAAC;AAAA,MACH;AACA,UAAI,KAAK,GAAG;AACV,QAAG,eAAe,MAAM,IAAI,EAAE,EAAE,YAAY,IAAI,YAAY,CAAC,IAAI,mBAAmB,EAAE,YAAY,IAAI,QAAQ;AAC9G,QAAG,YAAY,QAAQ,IAAI,QAAQ,IAAI,EAAE,YAAY,IAAI,sBAAsB,CAAC,EAAE,iBAAiB,IAAI,YAAY,IAAI,sBAAsB,SAAS,IAAI,EAAE,gBAAgB,IAAI,SAAS,IAAI,WAAW,OAAO,IAAI,UAAU,EAAE,iBAAiB,IAAI,QAAQ,EAAE,MAAM,IAAI,EAAE;AAC1Q,QAAG,YAAY,oBAAoB,IAAI,SAAS,EAAE,uCAAuC,IAAI,kBAAkB,IAAI,WAAW,EAAE,oCAAoC,IAAI,cAAc,EAAE,sCAAsC,IAAI,mBAAmB,EAAE,yBAAyB,IAAI,cAAc,EAAE,gCAAgC,IAAI,gBAAgB,CAAC;AAAA,MAC3V;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,UAAU;AAAA,MACV,IAAI;AAAA,MACJ,aAAa;AAAA,MACb,MAAM;AAAA,MACN,UAAU;AAAA,MACV,MAAM;AAAA,MACN,mBAAmB;AAAA,MACnB,qBAAqB,CAAC,GAAG,oBAAoB,qBAAqB;AAAA,MAClE,OAAO;AAAA,MACP,UAAU;AAAA,MACV,qBAAqB,CAAC,GAAG,uBAAuB,uBAAuB,gBAAgB;AAAA,IACzF;AAAA,IACA,UAAU,CAAC,UAAU;AAAA,IACrB,UAAU,CAAI,mBAAmB,CAAC;AAAA,MAChC,SAAS;AAAA,MACT,aAAa;AAAA,IACf,CAAC,CAAC,GAAM,oBAAoB;AAAA,EAC9B,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,UAAU,CAAC;AAAA,IACjF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA;AAAA,MAEV,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,SAAS;AAAA;AAAA;AAAA;AAAA,QAIT,4BAA4B;AAAA,QAC5B,+CAA+C;AAAA,QAC/C,4CAA4C;AAAA,QAC5C,8CAA8C;AAAA,QAC9C,iCAAiC;AAAA,QACjC,wCAAwC;AAAA;AAAA;AAAA,QAGxC,QAAQ;AAAA,QACR,cAAc;AAAA,QACd,cAAc;AAAA,QACd,eAAe;AAAA,QACf,mBAAmB;AAAA,QACnB,wBAAwB;AAAA;AAAA;AAAA,QAGxB,uBAAuB;AAAA,QACvB,wBAAwB;AAAA;AAAA;AAAA,QAGxB,aAAa;AAAA,QACb,WAAW;AAAA,QACX,UAAU;AAAA,QACV,WAAW;AAAA,MACb;AAAA,MACA,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,IAAI,CAAC;AAAA,MACH,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,qBAAqB,CAAC;AAAA,MACpB,MAAM;AAAA,MACN,MAAM,CAAC,kBAAkB;AAAA,IAC3B,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,qBAAqB,CAAC;AAAA,MACpB,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,iBAAN,MAAM,gBAAe;AAAA,EACnB,OAAO,OAAO,SAAS,uBAAuB,mBAAmB;AAC/D,WAAO,KAAK,qBAAqB,iBAAgB;AAAA,EACnD;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,iBAAiB,oBAAoB,QAAQ;AAAA,IACvD,SAAS,CAAC,UAAU,oBAAoB,iBAAiB,eAAe;AAAA,EAC1E,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,SAAS,CAAC,iBAAiB,oBAAoB,oBAAoB,iBAAiB,eAAe;AAAA,EACrG,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,iBAAiB,oBAAoB,QAAQ;AAAA,MACvD,SAAS,CAAC,UAAU,oBAAoB,iBAAiB,eAAe;AAAA,IAC1E,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": ["import_rxjs", "import_operators"]}