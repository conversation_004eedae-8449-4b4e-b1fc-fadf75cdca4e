using Core.Utilities.Paging;
using Core.Utilities.Results;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Business.Abstract
{
    public interface ICompanyExerciseService
    {
        IDataResult<List<CompanyExerciseDto>> GetCompanyExercises();
        IDataResult<List<CompanyExerciseDto>> GetCompanyExercisesByCategory(int categoryId);
        IDataResult<PaginatedResult<CompanyExerciseDto>> GetCompanyExercisesFiltered(CompanyExerciseFilterDto filter);
        IDataResult<List<CompanyExerciseDto>> SearchCompanyExercises(string searchTerm);
        IDataResult<CompanyExerciseDto> GetCompanyExerciseDetail(int exerciseId);
        IDataResult<CompanyExerciseDto> GetById(int exerciseId);
        IResult Add(CompanyExerciseAddDto exerciseAddDto);
        IResult Update(CompanyExerciseUpdateDto exerciseUpdateDto);
        IResult Delete(int exerciseId);
        
        // Birleşik egzersiz listesi (System + Company)
        IDataResult<List<CombinedExerciseDto>> GetCombinedExercises();
        IDataResult<List<CombinedExerciseDto>> GetCombinedExercisesByCategory(int categoryId);
        IDataResult<PaginatedResult<CombinedExerciseDto>> GetCombinedExercisesFiltered(SystemExerciseFilterDto filter);
    }
}
