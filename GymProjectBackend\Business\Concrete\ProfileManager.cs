using Business.Abstract;
using Business.BusinessAscpects.Autofac;
using Business.Constants;
using Core.Aspects.Autofac.Logging;
using Core.Aspects.Autofac.Performance;
using Core.Aspects.Autofac.Transaction;
using Core.Utilities.Results;
using DataAccess.Abstract;
using Microsoft.AspNetCore.Http;

namespace Business.Concrete
{
    public class ProfileManager : IProfileService
    {
        IUserDal _userDal;
        IFileService _fileService;
        IAdvancedRateLimitService _advancedRateLimitService;

        public ProfileManager(IUserDal userDal, IFileService fileService, IAdvancedRateLimitService advancedRateLimitService)
        {
            _userDal = userDal;
            _fileService = fileService;
            _advancedRateLimitService = advancedRateLimitService;
        }

        [SecuredOperation("owner,admin,member")]
        [LogAspect]
        [TransactionScopeAspect]
        [PerformanceAspect(3)]
        public IDataResult<string> UploadProfileImage(IFormFile file, int userId)
        {
            // Rate limiting kontrolü
            var rateLimitCheck = _advancedRateLimitService.CheckProfileImageUploadAttempt(userId);
            if (!rateLimitCheck.Success)
            {
                return new ErrorDataResult<string>(rateLimitCheck.Message);
            }

            var uploadResult = _fileService.UploadProfileImage(file, userId);
            if (!uploadResult.Success)
            {
                return uploadResult;
            }

            // SOLID prensiplerine uygun: Entity manipulation DAL katmanına devredildi
            var updateResult = _userDal.UpdateProfileImagePath(userId, uploadResult.Data);
            if (!updateResult.Success)
            {
                return new ErrorDataResult<string>(updateResult.Message);
            }

            // Başarılı upload kaydı
            _advancedRateLimitService.RecordProfileImageUpload(userId);

            return new SuccessDataResult<string>(uploadResult.Data, "Profil fotoğrafı başarıyla yüklendi.");
        }

        [SecuredOperation("owner,admin,member")]
        [LogAspect]
        [TransactionScopeAspect]
        [PerformanceAspect(3)]
        public IResult DeleteProfileImage(int userId)
        {
            var deleteResult = _fileService.DeleteProfileImage(userId);
            if (!deleteResult.Success)
            {
                return deleteResult;
            }

            // SOLID prensiplerine uygun: Entity manipulation DAL katmanına devredildi
            var clearResult = _userDal.ClearProfileImagePath(userId);
            if (!clearResult.Success)
            {
                return clearResult;
            }

            return new SuccessResult("Profil fotoğrafı başarıyla silindi.");
        }

        [SecuredOperation("owner,admin,member")]
        [LogAspect]
        [TransactionScopeAspect]
        [PerformanceAspect(3)]
        public IResult UpdateProfileImagePath(int userId, string imagePath)
        {
            // SOLID prensiplerine uygun: Entity manipulation DAL katmanına devredildi
            return _userDal.UpdateProfileImagePath(userId, imagePath);
        }
    }
}
