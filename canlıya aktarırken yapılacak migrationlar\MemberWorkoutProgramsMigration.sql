-- Üye Program Atama Sistemi Migration Script
-- Bu script üyelere antrenman programı atama sistemini oluşturur
-- MemberWorkoutPrograms: Üye-Program ilişki tablosu

USE [GymProject]
GO

-- 1. MemberWorkoutPrograms Tablosu (Üye-Program Atamaları)
CREATE TABLE [dbo].[MemberWorkoutPrograms](
    [MemberWorkoutProgramID] [int] IDENTITY(1,1) NOT NULL,
    [MemberID] [int] NOT NULL,
    [WorkoutProgramTemplateID] [int] NOT NULL,
    [CompanyID] [int] NOT NULL,

    [StartDate] [datetime2](7) NOT NULL, -- Program başlangıç tarihi
    [EndDate] [datetime2](7) NULL, -- Program bitiş tarihi (opsiyonel)
    [Notes] [nvarchar](1000) NULL, -- Atama notları
    [IsActive] [bit] NOT NULL DEFAULT(1), -- Aktif mi?
    [CreationDate] [datetime2](7) NULL DEFAULT(GETDATE()),
    [DeletedDate] [datetime2](7) NULL,
    [UpdatedDate] [datetime2](7) NULL,
    CONSTRAINT [PK_MemberWorkoutPrograms] PRIMARY KEY CLUSTERED ([MemberWorkoutProgramID] ASC),
    CONSTRAINT [FK_MemberWorkoutPrograms_Members] FOREIGN KEY([MemberID]) 
        REFERENCES [dbo].[Members] ([MemberID]) ON DELETE CASCADE,
    CONSTRAINT [FK_MemberWorkoutPrograms_WorkoutProgramTemplates] FOREIGN KEY([WorkoutProgramTemplateID]) 
        REFERENCES [dbo].[WorkoutProgramTemplates] ([WorkoutProgramTemplateID]) ON DELETE CASCADE,
    CONSTRAINT [FK_MemberWorkoutPrograms_Companies] FOREIGN KEY([CompanyID]) 
        REFERENCES [dbo].[Companies] ([CompanyID])
)
GO

-- 2. PERFORMANS İNDEXLERİ (10.000+ kullanıcı için optimizasyon)

-- Üye bazlı aktif programlar (en çok kullanılacak sorgu)
CREATE NONCLUSTERED INDEX [IX_MemberWorkoutPrograms_MemberID_IsActive]
ON [dbo].[MemberWorkoutPrograms] ([MemberID], [IsActive])
INCLUDE ([WorkoutProgramTemplateID], [StartDate], [EndDate], [CreationDate])
GO

-- Şirket bazlı atamalar (admin paneli için)
CREATE NONCLUSTERED INDEX [IX_MemberWorkoutPrograms_CompanyID_IsActive]
ON [dbo].[MemberWorkoutPrograms] ([CompanyID], [IsActive])
INCLUDE ([MemberID], [WorkoutProgramTemplateID], [CreationDate])
GO

-- Program bazlı atamalar (hangi programa kaç kişi atanmış)
CREATE NONCLUSTERED INDEX [IX_MemberWorkoutPrograms_TemplateID_IsActive]
ON [dbo].[MemberWorkoutPrograms] ([WorkoutProgramTemplateID], [IsActive])
INCLUDE ([MemberID], [CreationDate])
GO

-- Tarih bazlı sorgular için (oluşturma geçmişi)
CREATE NONCLUSTERED INDEX [IX_MemberWorkoutPrograms_CreationDate]
ON [dbo].[MemberWorkoutPrograms] ([CreationDate] DESC)
WHERE [IsActive] = 1
GO

-- Mobil API için User-Member-Program ilişkisi (composite index)
CREATE NONCLUSTERED INDEX [IX_MemberWorkoutPrograms_Member_Company_Active] 
ON [dbo].[MemberWorkoutPrograms] ([MemberID], [CompanyID], [IsActive])
INCLUDE ([WorkoutProgramTemplateID], [StartDate], [EndDate], [Notes])
GO

-- 3. KAPSAM KONTROLÜ (Constraint'ler)

-- StartDate EndDate'den küçük olmalı (eğer EndDate varsa)
ALTER TABLE [dbo].[MemberWorkoutPrograms]
ADD CONSTRAINT [CK_MemberWorkoutPrograms_DateRange] 
CHECK ([EndDate] IS NULL OR [StartDate] <= [EndDate])
GO

-- CreationDate gelecekte olamaz (5 saniye tolerans ile)
-- Bu tolerans C# DateTime.Now ile SQL GETDATE() arasındaki milisaniyelik farkları önler
ALTER TABLE [dbo].[MemberWorkoutPrograms]
ADD CONSTRAINT [CK_MemberWorkoutPrograms_CreationDate]
CHECK ([CreationDate] <= DATEADD(SECOND, 5, GETDATE()))
GO

PRINT 'Üye Program Atama Sistemi migration tamamlandı!'
PRINT 'Oluşturulan tablolar:'
PRINT '- MemberWorkoutPrograms (Üye-Program atamaları)'
PRINT 'Performans indexleri ve constraint''ler eklendi.'
PRINT 'Mobil API için optimizasyonlar hazır.'
GO
