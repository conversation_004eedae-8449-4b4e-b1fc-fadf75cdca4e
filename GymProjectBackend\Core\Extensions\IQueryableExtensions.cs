﻿using Core.Utilities.Paging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Extensions
{
    public static class IQueryableExtensions
    {
        public static PaginatedResult<T> ToPaginatedResult<T>(
            this IQueryable<T> source, int pageNumber, int pageSize)
        {
            var totalCount = source.Count();
            var items = source.Skip((pageNumber - 1) * pageSize).Take(pageSize).ToList();
            return new PaginatedResult<T>(items, pageNumber, pageSize, totalCount);
        }
    }

}
