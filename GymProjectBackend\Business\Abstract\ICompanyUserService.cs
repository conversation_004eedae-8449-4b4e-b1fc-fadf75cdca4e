﻿using Core.Entities.Concrete;
using Core.Utilities.Results;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Business.Abstract
{
    public interface ICompanyUserService
    {
        // Mevcut metodlar
        IDataResult<List<CompanyUser>> GetAll();
        IResult Add(CompanyUser companyUser);
        IResult Update(CompanyUser companyUser);
        IResult Delete(int id);
        IDataResult<List<CompanyUser>> GetByCityId(int cityId);
        IDataResult<List<CompanyDetailDto>> GetCompanyDetails();
        IDataResult<List<CompanyDetailDto>> GetCompanyUserDetailsByCityId(int cityId);
        IDataResult<List<CompanyUserDetailDto>> GetCompanyUserDetails();

        // Yeni eklenen metodlar
        IDataResult<CompanyUser> GetById(int companyUserID);
        IDataResult<CompanyUserFullDetailDto> GetCompanyUserFullDetails(int companyUserID);
        IResult UpdateCompanyUserFull(CompanyUserFullUpdateDto updateDto);
        IDataResult<PaginatedCompanyUserDto> GetCompanyUsersPaginated(int pageNumber, int pageSize, string searchTerm = "");

        // Soft delete metodları
        IResult SoftDeleteCompanyUser(int companyUserID);
        IDataResult<List<DeletedCompanyUserDto>> GetDeletedCompanyUsers();
        IResult RestoreCompanyUser(int companyUserID);
    }
}
