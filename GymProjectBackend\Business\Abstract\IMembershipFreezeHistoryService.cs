﻿using Core.Utilities.Results;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Business.Abstract
{
    public interface IMembershipFreezeHistoryService
    {
        IDataResult<List<MembershipFreezeHistoryDto>> GetAll();
        IDataResult<List<MembershipFreezeHistoryDto>> GetByMembershipId(int membershipId);
        IResult Add(MembershipFreezeHistory history);
        IResult Update(MembershipFreezeHistory history);
        IDataResult<int> GetRemainingFreezeDays(int membershipId);
    }

}
