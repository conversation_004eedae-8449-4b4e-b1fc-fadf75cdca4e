﻿using Core.Utilities.Results;
using Core.Utilities.Paging;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Business.Abstract
{
    public interface IMembershipTypeService
    {
        IDataResult<List<MembershipType>> GetAll();
        IDataResult<PaginatedResult<MembershipType>> GetAllPaginated(MembershipTypePagingParameters parameters);
        IResult Add(MembershipType membershipType);
        IResult Update(MembershipType membershipType);
        IResult Delete(int id);
        IDataResult<List<BranchGetAllDto>> GetBranchesAndTypes();
        IDataResult<List<PackageWithCountDto>> GetPackagesByBranch(string branch);

    }
}
