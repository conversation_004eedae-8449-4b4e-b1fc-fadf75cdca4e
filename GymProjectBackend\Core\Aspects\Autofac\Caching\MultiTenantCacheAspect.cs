using Castle.DynamicProxy;
using Core.CrossCuttingConcerns.Caching;
using Core.CrossCuttingConcerns.Caching.Configuration;
using Core.CrossCuttingConcerns.Caching.KeyGeneration;
using Core.Utilities.Interceptors;
using Core.Utilities.IoC;
using Core.Utilities.Security.CompanyContext;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Linq;

namespace Core.Aspects.Autofac.Caching
{
    /// <summary>
    /// Multi-tenant cache aspect - çok kiracılı cache sistemi için
    /// </summary>
    public class MultiTenantCacheAspect : MethodInterception
    {
        private readonly int _duration;
        private readonly string[] _tags;
        private readonly ICacheManager _cacheManager;
        private readonly ICacheKeyGenerator _keyGenerator;
        private readonly ICompanyContext _companyContext;
        private readonly CacheConfiguration _configuration;

        public MultiTenantCacheAspect(int duration = 60, params string[] tags)
        {
            _duration = duration;
            _tags = tags ?? Array.Empty<string>();
            _cacheManager = ServiceTool.ServiceProvider.GetService<ICacheManager>();
            _keyGenerator = ServiceTool.ServiceProvider.GetService<ICacheKeyGenerator>();
            _companyContext = ServiceTool.ServiceProvider.GetService<ICompanyContext>();
            _configuration = ServiceTool.ServiceProvider.GetService<CacheConfiguration>() ?? new CacheConfiguration();
        }

        public override void Intercept(IInvocation invocation)
        {
            try
            {
                // Tenant ID'sini al
                var tenantId = GetTenantId();
                if (tenantId <= 0)
                {
                    // Tenant ID geçersizse cache kullanma
                    invocation.Proceed();
                    return;
                }

                // Cache key oluştur
                var className = invocation.Method.ReflectedType?.FullName ?? "Unknown";
                var methodName = invocation.Method.Name;
                var arguments = invocation.Arguments ?? Array.Empty<object>();

                var cacheKey = _keyGenerator.GenerateKey(tenantId, className, methodName, arguments);

                // Cache'den kontrol et
                if (_cacheManager.IsAdd(cacheKey))
                {
                    var cachedResult = _cacheManager.Get(cacheKey);
                    if (cachedResult != null)
                    {
                        invocation.ReturnValue = cachedResult;
                        return;
                    }
                }

                // Cache'de yok, metodu çalıştır
                invocation.Proceed();

                // Sonucu cache'le
                if (invocation.ReturnValue != null)
                {
                    var entityName = ExtractEntityName(className);
                    var effectiveDuration = GetEffectiveDuration(entityName);
                    var effectiveTags = GetEffectiveTags(tenantId, entityName);

                    _cacheManager.Add(cacheKey, invocation.ReturnValue, effectiveDuration, effectiveTags);
                }
            }
            catch (Exception ex)
            {
                // Cache hatası durumunda metodu normal çalıştır
                System.Diagnostics.Debug.WriteLine($"Cache Aspect Error: {ex.Message}");
                invocation.Proceed();
            }
        }

        private int GetTenantId()
        {
            try
            {
                return _companyContext?.GetCompanyId() ?? -1;
            }
            catch
            {
                return -1;
            }
        }

        private string ExtractEntityName(string className)
        {
            if (string.IsNullOrEmpty(className))
                return "Unknown";

            var parts = className.Split('.');
            var managerName = parts.LastOrDefault() ?? className;

            if (managerName.EndsWith("Manager"))
                return managerName.Substring(0, managerName.Length - 7);

            if (managerName.EndsWith("Service"))
                return managerName.Substring(0, managerName.Length - 7);

            return managerName;
        }

        private int GetEffectiveDuration(string entityName)
        {
            if (_configuration.EntitySettings.TryGetValue(entityName, out var entitySettings))
            {
                return entitySettings.DefaultDuration;
            }

            return _duration > 0 ? _duration : _configuration.DefaultDurationMinutes;
        }

        private string[] GetEffectiveTags(int tenantId, string entityName)
        {
            var allTags = _keyGenerator.GenerateTags(tenantId, entityName, _tags);

            // Entity-specific tags ekle
            if (_configuration.EntitySettings.TryGetValue(entityName, out var entitySettings))
            {
                var entityTags = _keyGenerator.GenerateTags(tenantId, entityName, entitySettings.Tags);
                allTags = allTags.Concat(entityTags).Distinct().ToArray();
            }

            return allTags;
        }
    }
}
