﻿using Core.Utilities.Results;
using Entities.Concrete;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Business.Abstract
{
    public interface ILicensePackageService
    {
        IDataResult<List<LicensePackage>> GetAll();
        IDataResult<LicensePackage> GetById(int id);
        IResult Add(LicensePackage licensePackage);
        IResult Update(LicensePackage licensePackage);
        IResult Delete(int id);
    }
}
