﻿using Core.Utilities.Results;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Business.Abstract
{
    public interface IRemainingDebtService
    {
        IDataResult<List<RemainingDebtDetailDto>> GetRemainingDebtDetails();
        IResult AddDebtPayment(DebtPaymentDto debtPaymentDto);
        IResult Delete(int remainingDebtId);
    }
}
