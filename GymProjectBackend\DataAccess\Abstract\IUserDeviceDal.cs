﻿using Core.DataAccess;
using Core.Entities.Concrete;
using Core.Utilities.Results;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DataAccess.Abstract
{
    public interface IUserDeviceDal: IEntityRepository<UserDevice>
    {
        List<UserDevice> GetActiveDevicesByUserId(int userId);
        UserDevice GetByRefreshToken(string refreshToken);
        IResult AddDeviceWithManagement(UserDevice device);
        void CleanExpiredTokens();
        IResult RevokeAllDevicesExceptCurrent(int userId, string currentRefreshToken);
        IDataResult<UserDevice> GetByRefreshTokenWithValidation(string refreshToken);

        // SOLID prensiplerine uygun: Validation ve entity manipulation logic DAL katmanında
        IResult RevokeDeviceWithValidation(int deviceId);
    }
}
