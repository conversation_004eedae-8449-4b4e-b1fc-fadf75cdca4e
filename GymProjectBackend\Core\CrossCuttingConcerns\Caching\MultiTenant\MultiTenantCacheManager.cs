using Core.CrossCuttingConcerns.Caching.Configuration;
using Core.CrossCuttingConcerns.Caching.KeyGeneration;
using Core.CrossCuttingConcerns.Caching.Models;
using Core.Utilities.IoC;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;

namespace Core.CrossCuttingConcerns.Caching.MultiTenant
{
    public class MultiTenantCacheManager : ICacheManager
    {
        private readonly IMemoryCache _memoryCache;
        private readonly ICacheKeyGenerator _keyGenerator;
        private readonly CacheConfiguration _configuration;
        private readonly CacheStatistics _statistics;
        private readonly ConcurrentDictionary<string, CacheItem> _cacheItems;
        private readonly ConcurrentDictionary<string, HashSet<string>> _tagIndex;
        private readonly object _lockObject = new object();

        public MultiTenantCacheManager()
        {
            _memoryCache = ServiceTool.ServiceProvider.GetService<IMemoryCache>();
            _keyGenerator = ServiceTool.ServiceProvider.GetService<ICacheKeyGenerator>();
            _configuration = ServiceTool.ServiceProvider.GetService<CacheConfiguration>() ?? new CacheConfiguration();
            _statistics = new CacheStatistics();
            _cacheItems = new ConcurrentDictionary<string, CacheItem>();
            _tagIndex = new ConcurrentDictionary<string, HashSet<string>>();
        }

        public T Get<T>(string key)
        {
            try
            {
                var tenantId = _keyGenerator.ExtractTenantId(key);
                var entityName = _keyGenerator.ExtractEntityName(key);

                if (_memoryCache.TryGetValue(key, out T value))
                {
                    _statistics.RecordHit(tenantId, entityName);
                    LogDebug($"Cache HIT: {key}");
                    return value;
                }

                _statistics.RecordMiss(tenantId, entityName);
                LogDebug($"Cache MISS: {key}");
                return default(T);
            }
            catch (Exception ex)
            {
                LogDebug($"Cache GET Error: {key}, Error: {ex.Message}");
                return default(T);
            }
        }

        public object Get(string key)
        {
            return Get<object>(key);
        }

        public void Add(string key, object value, int durationMinutes)
        {
            Add(key, value, durationMinutes, null);
        }

        public void Add(string key, object value, int durationMinutes, string[] tags)
        {
            try
            {
                var tenantId = _keyGenerator.ExtractTenantId(key);
                var entityName = _keyGenerator.ExtractEntityName(key);

                var options = new MemoryCacheEntryOptions
                {
                    AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(durationMinutes),
                    Priority = CacheItemPriority.Normal
                };

                _memoryCache.Set(key, value, options);

                // Cache item bilgilerini sakla
                var cacheItem = new CacheItem
                {
                    Key = key,
                    TenantId = tenantId,
                    EntityName = entityName,
                    Tags = tags ?? Array.Empty<string>(),
                    CreatedAt = DateTime.UtcNow,
                    ExpiresAt = DateTime.UtcNow.AddMinutes(durationMinutes)
                };

                _cacheItems.TryAdd(key, cacheItem);

                // Tag index'i güncelle
                UpdateTagIndex(key, cacheItem.Tags);

                _statistics.RecordSet(tenantId, entityName);
                LogDebug($"Cache SET: {key}, Duration: {durationMinutes}min, Tags: [{string.Join(", ", tags ?? Array.Empty<string>())}]");
            }
            catch (Exception ex)
            {
                LogDebug($"Cache SET Error: {key}, Error: {ex.Message}");
            }
        }

        public bool IsAdd(string key)
        {
            return _memoryCache.TryGetValue(key, out _);
        }

        public void Remove(string key)
        {
            try
            {
                _memoryCache.Remove(key);

                if (_cacheItems.TryRemove(key, out var cacheItem))
                {
                    RemoveFromTagIndex(key, cacheItem.Tags);
                    _statistics.RecordRemoval(cacheItem.TenantId, cacheItem.EntityName);
                    LogDebug($"Cache REMOVE: {key}");
                }
            }
            catch (Exception ex)
            {
                LogDebug($"Cache REMOVE Error: {key}, Error: {ex.Message}");
            }
        }

        public void RemoveByPattern(string pattern)
        {
            try
            {
                var keysToRemove = GetKeysByPattern(pattern);
                LogDebug($"Cache REMOVE BY PATTERN: {pattern}, Found {keysToRemove.Length} keys");

                foreach (var key in keysToRemove)
                {
                    Remove(key);
                }
            }
            catch (Exception ex)
            {
                LogDebug($"Cache REMOVE BY PATTERN Error: {pattern}, Error: {ex.Message}");
            }
        }

        public void RemoveByTags(string[] tags)
        {
            try
            {
                var keysToRemove = new HashSet<string>();

                foreach (var tag in tags)
                {
                    if (_tagIndex.TryGetValue(tag, out var taggedKeys))
                    {
                        foreach (var key in taggedKeys)
                        {
                            keysToRemove.Add(key);
                        }
                    }
                }

                LogDebug($"Cache REMOVE BY TAGS: [{string.Join(", ", tags)}], Found {keysToRemove.Count} keys");

                foreach (var key in keysToRemove)
                {
                    Remove(key);
                }
            }
            catch (Exception ex)
            {
                LogDebug($"Cache REMOVE BY TAGS Error: [{string.Join(", ", tags)}], Error: {ex.Message}");
            }
        }

        public void RemoveByTenant(int tenantId)
        {
            var pattern = _keyGenerator.GeneratePattern(tenantId);
            RemoveByPattern(pattern);
        }

        public void RemoveByEntity(int tenantId, string entityName)
        {
            var pattern = _keyGenerator.GeneratePattern(tenantId, entityName);
            RemoveByPattern(pattern);
        }

        public void RemoveByEntityAndOperation(int tenantId, string entityName, string operation)
        {
            var pattern = _keyGenerator.GeneratePattern(tenantId, entityName, operation);
            RemoveByPattern(pattern);
        }

        public Dictionary<string, T> GetMultiple<T>(string[] keys)
        {
            var result = new Dictionary<string, T>();
            foreach (var key in keys)
            {
                var value = Get<T>(key);
                if (value != null)
                {
                    result[key] = value;
                }
            }
            return result;
        }

        public void AddMultiple(Dictionary<string, object> items, int durationMinutes)
        {
            foreach (var item in items)
            {
                Add(item.Key, item.Value, durationMinutes);
            }
        }

        public void RemoveMultiple(string[] keys)
        {
            foreach (var key in keys)
            {
                Remove(key);
            }
        }

        public void Clear()
        {
            lock (_lockObject)
            {
                _cacheItems.Clear();
                _tagIndex.Clear();

                // IMemoryCache'in Clear metodu yok, yeni instance oluşturmak gerekiyor
                // Bunun yerine tüm key'leri tek tek siliyoruz
                var allKeys = GetAllKeys();
                foreach (var key in allKeys)
                {
                    _memoryCache.Remove(key);
                }

                LogDebug("Cache CLEARED");
            }
        }

        public void ClearTenant(int tenantId)
        {
            RemoveByTenant(tenantId);
        }

        public long GetCacheSize()
        {
            return _cacheItems.Count;
        }

        public string[] GetAllKeys()
        {
            return _cacheItems.Keys.ToArray();
        }

        public string[] GetKeysByPattern(string pattern)
        {
            try
            {
                // Wildcard pattern'i regex'e çevir
                var regexPattern = "^" + Regex.Escape(pattern).Replace("\\*", ".*") + "$";
                var regex = new Regex(regexPattern, RegexOptions.IgnoreCase | RegexOptions.Compiled);

                return _cacheItems.Keys.Where(key => regex.IsMatch(key)).ToArray();
            }
            catch
            {
                // Regex hatası durumunda basit string matching
                return _cacheItems.Keys.Where(key => key.Contains(pattern.Replace("*", ""))).ToArray();
            }
        }

        public CacheStatistics GetStatistics()
        {
            return _statistics;
        }

        public void ResetStatistics()
        {
            _statistics.Reset();
        }

        public bool IsHealthy()
        {
            try
            {
                var testKey = "health_check_" + Guid.NewGuid();
                var testValue = "test";

                Add(testKey, testValue, 1);
                var retrieved = Get<string>(testKey);
                Remove(testKey);

                return retrieved == testValue;
            }
            catch
            {
                return false;
            }
        }

        public Dictionary<string, object> GetHealthInfo()
        {
            return new Dictionary<string, object>
            {
                ["IsHealthy"] = IsHealthy(),
                ["CacheSize"] = GetCacheSize(),
                ["Statistics"] = GetStatistics(),
                ["Configuration"] = _configuration,
                ["MemoryPressure"] = GC.GetTotalMemory(false)
            };
        }

        private void UpdateTagIndex(string key, string[] tags)
        {
            foreach (var tag in tags)
            {
                _tagIndex.AddOrUpdate(tag,
                    new HashSet<string> { key },
                    (_, existingSet) =>
                    {
                        lock (existingSet)
                        {
                            existingSet.Add(key);
                            return existingSet;
                        }
                    });
            }
        }

        private void RemoveFromTagIndex(string key, string[] tags)
        {
            foreach (var tag in tags)
            {
                if (_tagIndex.TryGetValue(tag, out var taggedKeys))
                {
                    lock (taggedKeys)
                    {
                        taggedKeys.Remove(key);
                        if (taggedKeys.Count == 0)
                        {
                            _tagIndex.TryRemove(tag, out _);
                        }
                    }
                }
            }
        }

        public List<int> GetActiveTenants()
        {
            try
            {
                return _cacheItems.Values
                    .Select(item => item.TenantId)
                    .Where(tenantId => tenantId > 0)
                    .Distinct()
                    .OrderBy(id => id)
                    .ToList();
            }
            catch (Exception ex)
            {
                LogDebug($"GetActiveTenants Error: {ex.Message}");
                return new List<int>();
            }
        }

        public Dictionary<string, int> GetEntityCounts(int tenantId)
        {
            try
            {
                return _cacheItems.Values
                    .Where(item => item.TenantId == tenantId)
                    .GroupBy(item => item.EntityName)
                    .ToDictionary(g => g.Key, g => g.Count());
            }
            catch (Exception ex)
            {
                LogDebug($"GetEntityCounts Error: {ex.Message}");
                return new Dictionary<string, int>();
            }
        }

        public List<CacheItemDetail> GetCacheDetails(int tenantId)
        {
            try
            {
                var details = new List<CacheItemDetail>();

                foreach (var item in _cacheItems.Values.Where(i => i.TenantId == tenantId))
                {
                    var detail = new CacheItemDetail
                    {
                        Key = item.Key,
                        TenantId = item.TenantId,
                        EntityName = item.EntityName,
                        Tags = item.Tags,
                        CreatedAt = item.CreatedAt,
                        ExpiresAt = item.ExpiresAt,
                        SizeInBytes = EstimateObjectSize(item.Key),
                        ValueType = GetValueType(item.Key),
                        AccessCount = 0, // Bu bilgiyi tutmak için ek yapı gerekir
                        LastAccessedAt = DateTime.UtcNow
                    };
                    details.Add(detail);
                }

                return details.OrderByDescending(d => d.CreatedAt).ToList();
            }
            catch (Exception ex)
            {
                LogDebug($"GetCacheDetails Error: {ex.Message}");
                return new List<CacheItemDetail>();
            }
        }

        public CacheItemDetail GetCacheItemDetail(string key)
        {
            try
            {
                if (_cacheItems.TryGetValue(key, out var item))
                {
                    return new CacheItemDetail
                    {
                        Key = item.Key,
                        TenantId = item.TenantId,
                        EntityName = item.EntityName,
                        Tags = item.Tags,
                        CreatedAt = item.CreatedAt,
                        ExpiresAt = item.ExpiresAt,
                        SizeInBytes = EstimateObjectSize(key),
                        ValueType = GetValueType(key),
                        AccessCount = 0,
                        LastAccessedAt = DateTime.UtcNow
                    };
                }
                return null;
            }
            catch (Exception ex)
            {
                LogDebug($"GetCacheItemDetail Error: {ex.Message}");
                return null;
            }
        }

        public long GetTenantCacheSize(int tenantId)
        {
            try
            {
                return _cacheItems.Values
                    .Where(item => item.TenantId == tenantId)
                    .Sum(item => EstimateObjectSize(item.Key));
            }
            catch (Exception ex)
            {
                LogDebug($"GetTenantCacheSize Error: {ex.Message}");
                return 0;
            }
        }

        public Dictionary<int, long> GetAllTenantSizes()
        {
            try
            {
                return _cacheItems.Values
                    .Where(item => item.TenantId > 0)
                    .GroupBy(item => item.TenantId)
                    .ToDictionary(g => g.Key, g => g.Sum(item => EstimateObjectSize(item.Key)));
            }
            catch (Exception ex)
            {
                LogDebug($"GetAllTenantSizes Error: {ex.Message}");
                return new Dictionary<int, long>();
            }
        }

        public Dictionary<string, object> GetPerformanceMetrics()
        {
            try
            {
                var stats = GetStatistics();
                var totalMemory = GC.GetTotalMemory(false);
                var cacheSize = GetCacheSize();

                return new Dictionary<string, object>
                {
                    ["TotalMemoryUsage"] = totalMemory,
                    ["CacheMemoryUsage"] = cacheSize,
                    ["CacheMemoryPercentage"] = totalMemory > 0 ? (double)cacheSize / totalMemory * 100 : 0,
                    ["TotalKeys"] = _cacheItems.Count,
                    ["HitRatio"] = stats.HitRatio,
                    ["TotalHits"] = stats.TotalHits,
                    ["TotalMisses"] = stats.TotalMisses,
                    ["ActiveTenants"] = GetActiveTenants().Count,
                    ["AverageKeySize"] = _cacheItems.Count > 0 ? cacheSize / _cacheItems.Count : 0,
                    ["ExpiredKeys"] = _cacheItems.Values.Count(item => DateTime.UtcNow > item.ExpiresAt)
                };
            }
            catch (Exception ex)
            {
                LogDebug($"GetPerformanceMetrics Error: {ex.Message}");
                return new Dictionary<string, object>();
            }
        }

        private long EstimateObjectSize(string key)
        {
            try
            {
                if (_memoryCache.TryGetValue(key, out var value))
                {
                    if (value == null) return 0;

                    // Basit boyut tahmini
                    var json = System.Text.Json.JsonSerializer.Serialize(value);
                    return System.Text.Encoding.UTF8.GetByteCount(json);
                }
                return 0;
            }
            catch
            {
                return 100; // Varsayılan boyut
            }
        }

        private string GetValueType(string key)
        {
            try
            {
                if (_memoryCache.TryGetValue(key, out var value))
                {
                    return value?.GetType().Name ?? "null";
                }
                return "Unknown";
            }
            catch
            {
                return "Error";
            }
        }

        private void LogDebug(string message)
        {
            if (_configuration.EnableDebugLogging)
            {
                Debug.WriteLine($"[MultiTenantCache] {DateTime.Now:HH:mm:ss.fff} - {message}");
            }
        }
    }

    public class CacheItem
    {
        public string Key { get; set; }
        public int TenantId { get; set; }
        public string EntityName { get; set; }
        public string[] Tags { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime ExpiresAt { get; set; }
    }
}
