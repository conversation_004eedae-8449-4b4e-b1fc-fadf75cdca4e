import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { BaseApiService } from './baseApiService';
import { ListResponseModel } from '../models/listResponseModel';
import { SingleResponseModel } from '../models/singleResponseModel';
import { ResponseModel } from '../models/responseModel';

export interface CacheStatistics {
  totalHits: number;
  totalMisses: number;
  hitRatio: number;
  totalSets: number;
  totalKeys: number;
  tenantStatistics: { [key: string]: any };
  entityStatistics: { [key: string]: any };
}

export interface CacheItemDetail {
  key: string;
  tenantId: number;
  entityName: string;
  tags: string[];
  createdAt: string;
  expiresAt: string;
  timeToLive: string;
  isExpired: boolean;
  sizeInBytes: number;
  valueType: string;
  accessCount: number;
  lastAccessedAt: string;
  formattedSize: string;
  formattedTTL: string;
  status: string;
}

export interface TenantCacheInfo {
  tenantId: number;
  cacheSize: number;
  keyCount: number;
  entities: { [key: string]: number };
  hitRatio: number;
}

export interface PerformanceMetrics {
  totalMemoryUsage: number;
  cacheMemoryUsage: number;
  cacheMemoryPercentage: number;
  totalKeys: number;
  hitRatio: number;
  totalHits: number;
  totalMisses: number;
  activeTenants: number;
  averageKeySize: number;
  expiredKeys: number;
}

@Injectable({
  providedIn: 'root'
})
export class CacheAdminService extends BaseApiService {

  constructor(private httpClient: HttpClient) {
    super();
  }

  // Cache istatistikleri
  getStatistics(): Observable<SingleResponseModel<CacheStatistics>> {
    return this.httpClient.get<SingleResponseModel<CacheStatistics>>(
      `${this.apiUrl}cacheadmin/statistics`
    );
  }

  // Cache sağlık durumu
  getHealthInfo(): Observable<SingleResponseModel<{ [key: string]: any }>> {
    return this.httpClient.get<SingleResponseModel<{ [key: string]: any }>>(
      `${this.apiUrl}cacheadmin/health`
    );
  }

  // Tüm cache anahtarları
  getAllKeys(): Observable<SingleResponseModel<string[]>> {
    return this.httpClient.get<SingleResponseModel<string[]>>(
      `${this.apiUrl}cacheadmin/keys`
    );
  }

  // Pattern ile cache anahtarları
  getKeysByPattern(pattern: string): Observable<SingleResponseModel<string[]>> {
    return this.httpClient.get<SingleResponseModel<string[]>>(
      `${this.apiUrl}cacheadmin/keys/pattern/${encodeURIComponent(pattern)}`
    );
  }

  // Cache boyutu
  getCacheSize(): Observable<SingleResponseModel<number>> {
    return this.httpClient.get<SingleResponseModel<number>>(
      `${this.apiUrl}cacheadmin/size`
    );
  }

  // Tüm cache'i temizle
  clearAll(): Observable<ResponseModel> {
    return this.httpClient.delete<ResponseModel>(
      `${this.apiUrl}cacheadmin/clear`
    );
  }

  // Tenant cache'ini temizle
  clearTenant(tenantId: number): Observable<ResponseModel> {
    return this.httpClient.delete<ResponseModel>(
      `${this.apiUrl}cacheadmin/clear/tenant/${tenantId}`
    );
  }

  // Entity cache'ini temizle
  clearEntity(tenantId: number, entityName: string): Observable<ResponseModel> {
    return this.httpClient.delete<ResponseModel>(
      `${this.apiUrl}cacheadmin/clear/entity/${tenantId}/${encodeURIComponent(entityName)}`
    );
  }

  // Cache test
  testCache(): Observable<ResponseModel> {
    return this.httpClient.get<ResponseModel>(
      `${this.apiUrl}cacheadmin/test`
    );
  }

  // Gelişmiş cache yönetimi
  getDetailedStatistics(): Observable<SingleResponseModel<any>> {
    return this.httpClient.get<SingleResponseModel<any>>(
      `${this.apiUrl}cacheadmin/statistics/detailed`
    );
  }

  getTenantStatistics(tenantId: number): Observable<SingleResponseModel<CacheStatistics>> {
    return this.httpClient.get<SingleResponseModel<CacheStatistics>>(
      `${this.apiUrl}cacheadmin/statistics/tenant/${tenantId}`
    );
  }

  getActiveTenants(): Observable<SingleResponseModel<number[]>> {
    return this.httpClient.get<SingleResponseModel<number[]>>(
      `${this.apiUrl}cacheadmin/tenants`
    );
  }

  getAllTenantSizes(): Observable<SingleResponseModel<{ [key: number]: number }>> {
    return this.httpClient.get<SingleResponseModel<{ [key: number]: number }>>(
      `${this.apiUrl}cacheadmin/tenants/sizes`
    );
  }

  getTenantKeys(tenantId: number): Observable<SingleResponseModel<string[]>> {
    return this.httpClient.get<SingleResponseModel<string[]>>(
      `${this.apiUrl}cacheadmin/tenant/${tenantId}/keys`
    );
  }

  getTenantCacheSize(tenantId: number): Observable<SingleResponseModel<number>> {
    return this.httpClient.get<SingleResponseModel<number>>(
      `${this.apiUrl}cacheadmin/tenant/${tenantId}/size`
    );
  }

  getEntityCounts(tenantId: number): Observable<SingleResponseModel<{ [key: string]: number }>> {
    return this.httpClient.get<SingleResponseModel<{ [key: string]: number }>>(
      `${this.apiUrl}cacheadmin/tenant/${tenantId}/entities`
    );
  }

  getCacheDetails(tenantId: number): Observable<SingleResponseModel<CacheItemDetail[]>> {
    return this.httpClient.get<SingleResponseModel<CacheItemDetail[]>>(
      `${this.apiUrl}cacheadmin/tenant/${tenantId}/details`
    );
  }

  getEntityKeys(tenantId: number, entityName: string): Observable<SingleResponseModel<string[]>> {
    return this.httpClient.get<SingleResponseModel<string[]>>(
      `${this.apiUrl}cacheadmin/tenant/${tenantId}/entity/${entityName}/keys`
    );
  }

  getCacheItemDetail(key: string): Observable<SingleResponseModel<CacheItemDetail>> {
    return this.httpClient.get<SingleResponseModel<CacheItemDetail>>(
      `${this.apiUrl}cacheadmin/item/${encodeURIComponent(key)}`
    );
  }

  getPerformanceMetrics(): Observable<SingleResponseModel<PerformanceMetrics>> {
    return this.httpClient.get<SingleResponseModel<PerformanceMetrics>>(
      `${this.apiUrl}cacheadmin/performance`
    );
  }
}
