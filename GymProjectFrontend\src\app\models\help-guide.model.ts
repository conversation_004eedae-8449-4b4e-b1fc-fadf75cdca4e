// src/app/models/help-guide.model.ts
export interface HelpGuide {
  id: string;
  title: string;
  description: string;
  content: HelpContent[];
  category: HelpCategory;
  targetRoles: string[];
  icon: string;
  priority: number;
}

export interface HelpContent {
  type: 'text' | 'list' | 'steps' | 'warning' | 'tip';
  content: string | string[];
  icon?: string;
}

export enum HelpCategory {
  CUSTOMER_MANAGEMENT = 'customer-management',
  E_MONEY = 'e-money',
  TRAINING = 'training',
  GYM_MANAGEMENT = 'gym-management',
  LICENSE_MANAGEMENT = 'license-management',
  SYSTEM_MANAGEMENT = 'system-management',
  GENERAL = 'general'
}

export interface HelpDialogData {
  guide: HelpGuide;
  showCloseButton?: boolean;
}
