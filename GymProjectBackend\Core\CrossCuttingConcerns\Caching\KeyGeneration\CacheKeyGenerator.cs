using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Text;

namespace Core.CrossCuttingConcerns.Caching.KeyGeneration
{
    public class CacheKeyGenerator : ICacheKeyGenerator
    {
        private const string SEPARATOR = ":";
        private const string TENANT_PREFIX = "T";
        private const string ENTITY_PREFIX = "E";
        private const string METHOD_PREFIX = "M";
        private const string PARAM_PREFIX = "P";
        
        public string GenerateKey(int tenantId, string className, string methodName, object[] arguments)
        {
            var keyBuilder = new StringBuilder();
            
            // Tenant prefix
            keyBuilder.Append($"{TENANT_PREFIX}{tenantId}{SEPARATOR}");
            
            // Entity name (class name'den çıkar)
            var entityName = ExtractEntityNameFromClassName(className);
            keyBuilder.Append($"{ENTITY_PREFIX}{entityName}{SEPARATOR}");
            
            // Method name
            keyBuilder.Append($"{METHOD_PREFIX}{methodName}{SEPARATOR}");
            
            // Parameters
            if (arguments != null && arguments.Length > 0)
            {
                var paramHash = GenerateParameterHash(arguments);
                keyBuilder.Append($"{PARAM_PREFIX}{paramHash}");
            }
            
            return keyBuilder.ToString();
        }
        
        public string GenerateEntityKey(int tenantId, string entityName, string operation, Dictionary<string, object> parameters = null)
        {
            var keyBuilder = new StringBuilder();
            
            keyBuilder.Append($"{TENANT_PREFIX}{tenantId}{SEPARATOR}");
            keyBuilder.Append($"{ENTITY_PREFIX}{entityName}{SEPARATOR}");
            keyBuilder.Append($"{METHOD_PREFIX}{operation}");
            
            if (parameters != null && parameters.Any())
            {
                keyBuilder.Append(SEPARATOR);
                var sortedParams = parameters.OrderBy(p => p.Key);
                var paramString = string.Join("&", sortedParams.Select(p => $"{p.Key}={p.Value}"));
                var paramHash = GenerateStringHash(paramString);
                keyBuilder.Append($"{PARAM_PREFIX}{paramHash}");
            }
            
            return keyBuilder.ToString();
        }
        
        public string[] GenerateTags(int tenantId, string entityName, string[] additionalTags = null)
        {
            var tags = new List<string>
            {
                $"{TENANT_PREFIX}{tenantId}",
                $"{ENTITY_PREFIX}{entityName}",
                $"{TENANT_PREFIX}{tenantId}{SEPARATOR}{ENTITY_PREFIX}{entityName}"
            };
            
            if (additionalTags != null)
            {
                foreach (var tag in additionalTags)
                {
                    tags.Add($"{TENANT_PREFIX}{tenantId}{SEPARATOR}{tag}");
                    tags.Add(tag);
                }
            }
            
            return tags.ToArray();
        }
        
        public string GeneratePattern(int tenantId, string entityName = null, string operation = null)
        {
            var pattern = $"{TENANT_PREFIX}{tenantId}{SEPARATOR}";
            
            if (!string.IsNullOrEmpty(entityName))
            {
                pattern += $"{ENTITY_PREFIX}{entityName}{SEPARATOR}";
                
                if (!string.IsNullOrEmpty(operation))
                {
                    pattern += $"{METHOD_PREFIX}{operation}{SEPARATOR}";
                }
            }
            
            return pattern + "*";
        }
        
        public int ExtractTenantId(string key)
        {
            if (string.IsNullOrEmpty(key) || !key.StartsWith(TENANT_PREFIX))
                return -1;
                
            var firstSeparator = key.IndexOf(SEPARATOR);
            if (firstSeparator <= 1)
                return -1;
                
            var tenantIdStr = key.Substring(1, firstSeparator - 1);
            return int.TryParse(tenantIdStr, out int tenantId) ? tenantId : -1;
        }
        
        public string ExtractEntityName(string key)
        {
            if (string.IsNullOrEmpty(key))
                return string.Empty;
                
            var parts = key.Split(SEPARATOR);
            if (parts.Length < 2)
                return string.Empty;
                
            var entityPart = parts[1];
            return entityPart.StartsWith(ENTITY_PREFIX) ? entityPart.Substring(1) : string.Empty;
        }
        
        private string ExtractEntityNameFromClassName(string className)
        {
            if (string.IsNullOrEmpty(className))
                return "Unknown";
                
            // Business.Concrete.MemberManager -> Member
            var parts = className.Split('.');
            var managerName = parts.LastOrDefault() ?? className;
            
            if (managerName.EndsWith("Manager"))
                return managerName.Substring(0, managerName.Length - 7);
                
            if (managerName.EndsWith("Service"))
                return managerName.Substring(0, managerName.Length - 7);
                
            return managerName;
        }
        
        private string GenerateParameterHash(object[] arguments)
        {
            var paramString = string.Join("|", arguments.Select(arg => arg?.ToString() ?? "null"));
            return GenerateStringHash(paramString);
        }
        
        private string GenerateStringHash(string input)
        {
            using (var sha256 = SHA256.Create())
            {
                var hashBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(input));
                return Convert.ToBase64String(hashBytes).Substring(0, 8); // İlk 8 karakter
            }
        }
    }
}
