﻿using Core.Entities.Concrete;
using Core.Utilities.IoC;
using DataAccess.Abstract;
using DataAccess.Concrete.EntityFramework;
using FluentValidation;
using Microsoft.Extensions.DependencyInjection;
using System.Linq;

namespace Business.ValidationRules.FluentValidation
{
    public class CompanyUserValidator : AbstractValidator<CompanyUser>
    {
        private readonly ICompanyUserDal _companyUserDal;

        public CompanyUserValidator()
        {
            // ServiceTool üzerinden servisleri al
            _companyUserDal = ServiceTool.ServiceProvider?.GetService<ICompanyUserDal>();

            RuleFor(p => p.Name).NotEmpty().WithMessage("İsim kısmı boş bırakılamaz.");
            RuleFor(p => p.PhoneNumber).NotEmpty().WithMessage("Telefon kısmı boş bırakılamaz.");
            RuleFor(p => p.PhoneNumber).Must((user, phone) => BeUniquePhoneNumber(user))
                .WithMessage("Bu telefon numarası sistemde zaten kayıtlı.");
            RuleFor(p => p.PhoneNumber).Length(11).WithMessage("Telefon numarasını kontrol ediniz.");
            RuleFor(p => p.PhoneNumber).Must(StartsWithZero).WithMessage("Telefon numarası 0 ile başlamak zorundadır");
            RuleFor(x => x.Email).EmailAddress().WithMessage("E Posta adresini doğru giriniz.");
            RuleFor(x => x).Must(BeUniqueEmail).WithMessage("Bu e-posta adresi sistemde zaten kayıtlı.");
            RuleFor(p => p.CityID).NotEmpty().WithMessage("İl kısmı boş bırakılamaz.");
            RuleFor(p => p.TownID).NotEmpty().WithMessage("İlçe kısmı boş bırakılamaz.");
        }

        private bool BeUniqueEmail(CompanyUser user)
        {
            // Null check for DI services - 1000+ salon sisteminde kritik
            if (_companyUserDal == null)
            {
                // Log the issue for monitoring in production
                System.Diagnostics.Debug.WriteLine("CompanyUserValidator: _companyUserDal is null - email validation bypassed");
                return true; // Validation geçer, başka katmanda kontrol edilir
            }

            // DI kullanılıyor - Scalability optimized
            if (user.CompanyUserID != 0)
            {
                return !_companyUserDal.GetAll(u =>
                    u.Email == user.Email &&
                    u.CompanyUserID != user.CompanyUserID &&
                    u.IsActive == true).Any();
            }
            else
            {
                return !_companyUserDal.GetAll(u =>
                    u.Email == user.Email &&
                    u.IsActive == true).Any();
            }
        }

        private bool BeUniquePhoneNumber(CompanyUser user)
        {
            // Null check for DI services - 1000+ salon sisteminde kritik
            if (_companyUserDal == null)
            {
                // Log the issue for monitoring in production
                System.Diagnostics.Debug.WriteLine("CompanyUserValidator: _companyUserDal is null - phone validation bypassed");
                return true; // Validation geçer, başka katmanda kontrol edilir
            }

            // DI kullanılıyor - Scalability optimized
            if (user.CompanyUserID != 0)
            {
                return !_companyUserDal.GetAll(u =>
                    u.PhoneNumber == user.PhoneNumber &&
                    u.CompanyUserID != user.CompanyUserID &&
                    u.IsActive == true).Any();
            }
            else
            {
                return !_companyUserDal.GetAll(u =>
                    u.PhoneNumber == user.PhoneNumber &&
                    u.IsActive == true).Any();
            }
        }

        private bool StartsWithZero(string arg)
        {
            return arg.StartsWith("0");
        }
    }
}