﻿using System;
using System.IO;

namespace Core.CrossCuttingConcerns.Logging.FileLogger
{
    public class PerformanceLoggerService : ILogService
    {
        private readonly string _logDirectory;
        private readonly string _performanceLogDirectory;
        private readonly object _lock = new object();

        public PerformanceLoggerService()
        {
            _logDirectory = @"C:\GymProjectLogs";
            _performanceLogDirectory = Path.Combine(_logDirectory, "PerformanceAspectLogs");
            Directory.CreateDirectory(_performanceLogDirectory);
        }

        private void Log(string level, string message)
        {
            var logFilePath = Path.Combine(_performanceLogDirectory, $"performance_log_{DateTime.Now:dd-MM-yyyy}.txt");
            var logMessage = $"{DateTime.Now:yyyy-MM-dd HH:mm:ss.fff} [{level}] {message}";

            lock (_lock)
            {
                try
                {
                    File.AppendAllText(logFilePath, logMessage + Environment.NewLine);
                }
                catch
                {
                    // Log dosyasına yazma hatası durumunda sessizce devam et
                }
            }
        }

        public void Info(string message, bool isPerformanceLog = false)
        {
            Log("INFO", message);
        }

        public void Debug(string message)
        {
            Log("DEBUG", message);
        }

        public void Warn(string message)
        {
            Log("WARN", message);
        }

        public void Error(string message)
        {
            Log("ERROR", message);
        }

        public void Fatal(string message)
        {
            Log("FATAL", message);
        }

        public void LogPerformance(string message)
        {
            Log("PERFORMANCE", message);
        }
    }
}