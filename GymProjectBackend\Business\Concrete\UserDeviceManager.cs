﻿using Business.Abstract;
using Business.BusinessAscpects.Autofac;
using Business.Constants;
using Core.Aspects.Autofac.Logging;
using Core.Entities.Concrete;
using Core.Utilities.Results;
using DataAccess.Abstract;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Business.Concrete
{
    public class UserDeviceManager : IUserDeviceService
    {
        private readonly IUserDeviceDal _userDeviceDal;

        public UserDeviceManager(IUserDeviceDal userDeviceDal)
        {
            _userDeviceDal = userDeviceDal;
        }

        public IResult Add(UserDevice device)
        {
            return _userDeviceDal.AddDeviceWithManagement(device);
        }


        public IResult Delete(int id)
        {
            var device = _userDeviceDal.Get(d => d.Id == id);
            if (device == null)
                return new ErrorResult(Messages.DeviceNotFound);

            _userDeviceDal.Delete(id);
            return new SuccessResult();
        }

        public IDataResult<List<UserDevice>> GetActiveDevicesByUserId(int userId)
        {
            var devices = _userDeviceDal.GetActiveDevicesByUserId(userId);
            return new SuccessDataResult<List<UserDevice>>(devices);
        }

        public IDataResult<UserDevice> GetByRefreshToken(string refreshToken)
        {
            return _userDeviceDal.GetByRefreshTokenWithValidation(refreshToken);
        }
        public IResult RevokeDevice(int deviceId)
        {
            // SOLID prensiplerine uygun: Validation ve entity manipulation logic'i DAL katmanına taşıdık
            return _userDeviceDal.RevokeDeviceWithValidation(deviceId);
        }

        //[SecuredOperation("owner,admin")]
        public IResult RevokeAllDevicesExceptCurrent(int userId, string currentRefreshToken)
        {
            return _userDeviceDal.RevokeAllDevicesExceptCurrent(userId, currentRefreshToken);
        }

        //[SecuredOperation("owner,admin")]
        public IResult Update(UserDevice device)
        {
            _userDeviceDal.Update(device);
            return new SuccessResult();
        }
    }
}
