/* Özellikler Tablosu Stilleri */

/* Faydalar Tablosu Stilleri */
.benefits-table-container {
    margin: 3rem 0;
    width: 100%;
}

.benefits-content {
    background-color: var(--bg-dark-tertiary);
    border-radius: var(--border-radius-lg);
    padding: 2.5rem;
    box-shadow: var(--card-shadow-dark);
    border: 1px solid var(--border-color-dark);
    margin-bottom: 2rem;
    text-align: center;
}

.benefits-content h3 {
    font-size: 2rem;
    margin-bottom: 1rem;
    color: var(--light-color);
}

.benefits-content p {
    font-size: 1.1rem;
    line-height: 1.7;
    color: var(--text-color-dark-secondary);
    max-width: 800px;
    margin: 0 auto;
}

.stats-boxes {
    display: flex;
    flex-wrap: wrap;
    gap: 2rem;
    justify-content: center;
}

.stats-boxes .stat-box {
    flex: 1;
    min-width: 250px;
    max-width: 350px;
    background-color: var(--bg-dark-tertiary);
    border-radius: var(--border-radius-lg);
    padding: 2.5rem;
    box-shadow: var(--card-shadow-dark);
    border: 1px solid var(--border-color-dark);
    text-align: center;
    transition: all var(--transition-speed) var(--transition-timing);
    position: relative;
    overflow: hidden;
}

.stats-boxes .stat-box::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(67, 97, 238, 0.05) 0%, rgba(76, 201, 240, 0.05) 100%);
    opacity: 0;
    transition: opacity var(--transition-speed) var(--transition-timing);
    z-index: 1;
    pointer-events: none;
}

.stats-boxes .stat-box:hover {
    transform: translateY(-10px);
    box-shadow: var(--card-shadow-dark-hover);
    border-color: var(--primary-color);
}

.stats-boxes .stat-box:hover::before {
    opacity: 1;
}

.stats-boxes .stat-icon {
    width: 70px;
    height: 70px;
    border-radius: 50%;
    background-color: rgba(67, 97, 238, 0.1);
    color: var(--primary-color);
    font-size: 1.8rem;
    margin: 0 auto 1.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all var(--transition-speed) var(--transition-timing);
    position: relative;
    z-index: 2;
    box-shadow: 0 5px 15px rgba(67, 97, 238, 0.2);
    border: 2px solid transparent;
}

.stats-boxes .stat-box:hover .stat-icon {
    background-color: var(--primary-color);
    color: var(--light-color);
    transform: scale(1.1);
    border-color: var(--accent-color);
    box-shadow: 0 8px 25px rgba(67, 97, 238, 0.4);
}

.stats-boxes .stat-number {
    font-size: 3rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
    position: relative;
    z-index: 2;
}

.stats-boxes .stat-box p {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--light-color);
    margin-bottom: 1rem;
    position: relative;
    z-index: 2;
}

.stats-boxes .stat-description {
    font-size: 1rem;
    color: var(--text-color-dark-secondary);
    line-height: 1.6;
    position: relative;
    z-index: 2;
}

/* Mobil Uyumluluk */
@media (max-width: 992px) {
    .stats-boxes {
        flex-direction: column;
        align-items: center;
    }

    .stats-boxes .stat-box {
        min-width: 100%;
    }
}

@media (max-width: 768px) {
    .benefits-content {
        padding: 2rem;
    }

    .benefits-content h3 {
        font-size: 1.8rem;
    }

    .benefits-content p {
        font-size: 1rem;
    }

    .stats-boxes .stat-box {
        padding: 2rem;
    }

    .stats-boxes .stat-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }

    .stats-boxes .stat-number {
        font-size: 2.5rem;
    }

    .stats-boxes .stat-box p {
        font-size: 1.1rem;
    }
}

@media (max-width: 576px) {
    .benefits-content {
        padding: 1.5rem;
    }

    .benefits-content h3 {
        font-size: 1.6rem;
    }

    .stats-boxes .stat-box {
        padding: 1.5rem;
    }

    .stats-boxes .stat-icon {
        width: 50px;
        height: 50px;
        font-size: 1.3rem;
        margin-bottom: 1rem;
    }

    .stats-boxes .stat-number {
        font-size: 2.2rem;
    }

    .stats-boxes .stat-box p {
        font-size: 1rem;
    }
}
.features-table-container {
    margin: 3rem 0;
    width: 100%;
}

.features-table {
    display: flex;
    flex-direction: column;
    gap: 2rem;
    width: 100%;
}

.features-row {
    display: flex;
    flex-wrap: wrap;
    gap: 2rem;
    width: 100%;
}

.feature-box {
    flex: 1;
    min-width: 300px;
    background-color: var(--bg-dark-tertiary);
    border-radius: var(--border-radius-lg);
    padding: 2.5rem;
    box-shadow: var(--card-shadow-dark);
    border: 1px solid var(--border-color-dark);
    transition: all var(--transition-speed) var(--transition-timing);
    position: relative;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.feature-box::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(67, 97, 238, 0.05) 0%, rgba(76, 201, 240, 0.05) 100%);
    opacity: 0;
    transition: opacity var(--transition-speed) var(--transition-timing);
    z-index: 1;
    pointer-events: none;
}

.feature-box:hover {
    transform: translateY(-10px);
    box-shadow: var(--card-shadow-dark-hover);
    border-color: var(--primary-color);
}

.feature-box:hover::before {
    opacity: 1;
}

.feature-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background-color: rgba(67, 97, 238, 0.1);
    color: var(--primary-color);
    font-size: 2.2rem;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all var(--transition-speed) var(--transition-timing);
    position: relative;
    z-index: 2;
    box-shadow: 0 5px 15px rgba(67, 97, 238, 0.2);
    border: 2px solid transparent;
}

.feature-box:hover .feature-icon {
    background-color: var(--primary-color);
    color: var(--light-color);
    transform: scale(1.1);
    border-color: var(--accent-color);
    box-shadow: 0 8px 25px rgba(67, 97, 238, 0.4);
}

.feature-box h3 {
    font-size: 1.4rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--light-color);
    position: relative;
    z-index: 2;
}

.feature-box p {
    font-size: 1rem;
    color: var(--text-color-dark-secondary);
    line-height: 1.6;
    margin-bottom: 1.5rem;
    position: relative;
    z-index: 2;
}

.feature-box .feature-list {
    margin: 1.5rem 0;
    position: relative;
    z-index: 2;
}

.feature-box .feature-list li {
    margin-bottom: 0.8rem;
    display: flex;
    align-items: flex-start;
    font-size: 1rem;
}

.feature-box .feature-list li i {
    color: var(--success-color);
    margin-right: 10px;
    font-size: 1rem;
    margin-top: 0.3rem;
}

.feature-box .feature-cta {
    margin-top: auto;
    align-self: flex-start;
    position: relative;
    z-index: 2;
}

/* Mobil Uyumluluk */
@media (max-width: 992px) {
    .features-row {
        flex-direction: column;
        gap: 1.5rem;
    }

    .feature-box {
        min-width: 100%;
    }
}

@media (max-width: 768px) {
    .feature-box {
        padding: 2rem;
    }

    .feature-icon {
        width: 70px;
        height: 70px;
        font-size: 1.8rem;
    }

    .feature-box h3 {
        font-size: 1.3rem;
    }

    .feature-box p {
        font-size: 0.95rem;
    }

    .feature-box .feature-list li {
        font-size: 0.95rem;
    }
}

@media (max-width: 576px) {
    .feature-box {
        padding: 1.5rem;
    }

    .feature-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
        margin-bottom: 1rem;
    }

    .feature-box h3 {
        font-size: 1.2rem;
    }

    .feature-box p {
        font-size: 0.9rem;
    }
}
