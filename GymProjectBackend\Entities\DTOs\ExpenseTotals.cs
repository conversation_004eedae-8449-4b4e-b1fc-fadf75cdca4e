using Core.Entities;

namespace Entities.DTOs
{
    public class ExpenseTotals : IDto
    {
        /// <summary>
        /// Kira giderleri toplamı
        /// </summary>
        public decimal Rent { get; set; }

        /// <summary>
        /// Fatura giderleri toplamı (Elektrik, Su, Gaz, İnternet vb.)
        /// </summary>
        public decimal Utilities { get; set; }

        /// <summary>
        /// Bakım-Onarım giderleri toplamı
        /// </summary>
        public decimal Maintenance { get; set; }

        /// <summary>
        /// Personel giderleri toplamı
        /// </summary>
        public decimal Staff { get; set; }

        /// <summary>
        /// Diğer giderler toplamı
        /// </summary>
        public decimal Other { get; set; }

        /// <summary>
        /// Toplam gider
        /// </summary>
        public decimal Total { get; set; }

        /// <summary>
        /// Gider türlerine göre detaylı dağılım
        /// </summary>
        public Dictionary<string, decimal> ExpenseTypeBreakdown { get; set; } = new Dictionary<string, decimal>();
    }
}
