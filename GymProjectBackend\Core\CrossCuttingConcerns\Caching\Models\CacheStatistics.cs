using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;

namespace Core.CrossCuttingConcerns.Caching.Models
{
    public class CacheStatistics
    {
        private readonly ConcurrentDictionary<int, TenantCacheStats> _tenantStats;
        private readonly ConcurrentDictionary<string, EntityCacheStats> _entityStats;
        private readonly object _lockObject = new object();
        
        public CacheStatistics()
        {
            _tenantStats = new ConcurrentDictionary<int, TenantCacheStats>();
            _entityStats = new ConcurrentDictionary<string, EntityCacheStats>();
            StartTime = DateTime.UtcNow;
        }
        
        public DateTime StartTime { get; }
        public long TotalHits { get; private set; }
        public long TotalMisses { get; private set; }
        public long TotalSets { get; private set; }
        public long TotalRemovals { get; private set; }
        
        public double HitRatio => TotalHits + TotalMisses > 0 ? (double)TotalHits / (TotalHits + TotalMisses) : 0;
        
        public void RecordHit(int tenantId, string entityName)
        {
            lock (_lockObject)
            {
                TotalHits++;
                GetTenantStats(tenantId).RecordHit();
                GetEntityStats(entityName).RecordHit();
            }
        }
        
        public void RecordMiss(int tenantId, string entityName)
        {
            lock (_lockObject)
            {
                TotalMisses++;
                GetTenantStats(tenantId).RecordMiss();
                GetEntityStats(entityName).RecordMiss();
            }
        }
        
        public void RecordSet(int tenantId, string entityName)
        {
            lock (_lockObject)
            {
                TotalSets++;
                GetTenantStats(tenantId).RecordSet();
                GetEntityStats(entityName).RecordSet();
            }
        }
        
        public void RecordRemoval(int tenantId, string entityName, int count = 1)
        {
            lock (_lockObject)
            {
                TotalRemovals += count;
                GetTenantStats(tenantId).RecordRemoval(count);
                GetEntityStats(entityName).RecordRemoval(count);
            }
        }
        
        public TenantCacheStats GetTenantStats(int tenantId)
        {
            return _tenantStats.GetOrAdd(tenantId, _ => new TenantCacheStats(tenantId));
        }
        
        public EntityCacheStats GetEntityStats(string entityName)
        {
            return _entityStats.GetOrAdd(entityName, _ => new EntityCacheStats(entityName));
        }
        
        public Dictionary<int, TenantCacheStats> GetAllTenantStats()
        {
            return _tenantStats.ToDictionary(kvp => kvp.Key, kvp => kvp.Value);
        }
        
        public Dictionary<string, EntityCacheStats> GetAllEntityStats()
        {
            return _entityStats.ToDictionary(kvp => kvp.Key, kvp => kvp.Value);
        }
        
        public void Reset()
        {
            lock (_lockObject)
            {
                TotalHits = 0;
                TotalMisses = 0;
                TotalSets = 0;
                TotalRemovals = 0;
                _tenantStats.Clear();
                _entityStats.Clear();
            }
        }
    }
    
    public class TenantCacheStats
    {
        public int TenantId { get; }
        public long Hits { get; private set; }
        public long Misses { get; private set; }
        public long Sets { get; private set; }
        public long Removals { get; private set; }
        public DateTime LastAccess { get; private set; }
        
        public double HitRatio => Hits + Misses > 0 ? (double)Hits / (Hits + Misses) : 0;
        
        public TenantCacheStats(int tenantId)
        {
            TenantId = tenantId;
            LastAccess = DateTime.UtcNow;
        }
        
        public void RecordHit()
        {
            Hits++;
            LastAccess = DateTime.UtcNow;
        }
        
        public void RecordMiss()
        {
            Misses++;
            LastAccess = DateTime.UtcNow;
        }
        
        public void RecordSet()
        {
            Sets++;
            LastAccess = DateTime.UtcNow;
        }
        
        public void RecordRemoval(int count = 1)
        {
            Removals += count;
            LastAccess = DateTime.UtcNow;
        }
    }
    
    public class EntityCacheStats
    {
        public string EntityName { get; }
        public long Hits { get; private set; }
        public long Misses { get; private set; }
        public long Sets { get; private set; }
        public long Removals { get; private set; }
        public DateTime LastAccess { get; private set; }
        
        public double HitRatio => Hits + Misses > 0 ? (double)Hits / (Hits + Misses) : 0;
        
        public EntityCacheStats(string entityName)
        {
            EntityName = entityName;
            LastAccess = DateTime.UtcNow;
        }
        
        public void RecordHit()
        {
            Hits++;
            LastAccess = DateTime.UtcNow;
        }
        
        public void RecordMiss()
        {
            Misses++;
            LastAccess = DateTime.UtcNow;
        }
        
        public void RecordSet()
        {
            Sets++;
            LastAccess = DateTime.UtcNow;
        }
        
        public void RecordRemoval(int count = 1)
        {
            Removals += count;
            LastAccess = DateTime.UtcNow;
        }
    }
}
