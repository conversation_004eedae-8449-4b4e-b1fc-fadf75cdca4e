-- Profil Fotoğrafı Migration
-- Users tablosuna ProfileImagePath alanı ekleme

USE [GymProject]
GO

-- Users tablosuna ProfileImagePath alanını ekle
ALTER TABLE [dbo].[Users]
ADD [ProfileImagePath] [varchar](500) NULL
GO

-- Mevcut kullanıcılar i<PERSON>in <PERSON> (null olarak kalacak)
-- Null değeri frontend'de varsayılan font-awesome ikonu gösterecek

-- Index ekleme (performans için)
CREATE NONCLUSTERED INDEX [IX_Users_ProfileImagePath] ON [dbo].[Users]
(
    [ProfileImagePath] ASC
)
WHERE [ProfileImagePath] IS NOT NULL
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
GO

-- Yorum: ProfileImagePath alanı kullanıcının profil fotoğrafının dosya yolunu tutar
-- Format: "Images/{UserID}.{extension}" şeklinde olacak
-- Null değeri varsayılan font-awesome user ikonunu gösterir
