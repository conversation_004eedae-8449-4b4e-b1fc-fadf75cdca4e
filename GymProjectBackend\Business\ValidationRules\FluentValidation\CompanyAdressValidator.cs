﻿using Core.Utilities.IoC;
using DataAccess.Abstract;
using DataAccess.Concrete.EntityFramework;
using Entities.Concrete;
using FluentValidation;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Business.ValidationRules.FluentValidation
{
   public class CompanyAdressValidator:AbstractValidator<CompanyAdress>
    {
        private readonly ICompanyAdressDal _companyAdressDal;

        public CompanyAdressValidator()
        {
            // ServiceTool üzerinden servisleri al
            _companyAdressDal = ServiceTool.ServiceProvider?.GetService<ICompanyAdressDal>();

            RuleFor(c => c.Adress).NotEmpty().WithMessage("Adres boş bırakılamaz.");
            RuleFor(c => c.CompanyID).NotEmpty().WithMessage("Şirket ismi boş bırakılamaz.");
            RuleFor(c => c.CityID).NotEmpty().WithMessage("İl kısmı boş bırakılamaz.");
            RuleFor(c => c.TownID).NotEmpty().WithMessage("İlçe kısmı boş bırakılamaz.");
            RuleFor(c => c).Must(BeUniqueAdress).WithMessage("Bu şirkete zaten adres verilmiş.");

        }
        private bool BeUniqueAdress(CompanyAdress adress)
        {
            // Null check for DI services - 1000+ salon sisteminde kritik
            if (_companyAdressDal == null)
            {
                // Log the issue for monitoring in production
                System.Diagnostics.Debug.WriteLine("CompanyAdressValidator: _companyAdressDal is null - validation bypassed");
                return true; // Validation geçer, başka katmanda kontrol edilir
            }

            // DI kullanılıyor - Scalability optimized
            if (adress.CompanyAdressID != 0)
            {
                return !_companyAdressDal.GetAll(c =>
                    c.Adress == adress.Adress &&
                    c.CompanyID == adress.CompanyID &&
                    c.CompanyAdressID != adress.CompanyAdressID &&
                    c.IsActive == true).Any();
            }
            else
            {
                return !_companyAdressDal.GetAll(c =>
                    c.Adress == adress.Adress &&
                    c.CompanyID == adress.CompanyID &&
                    c.IsActive == true).Any();
            }
        }
    }
}
