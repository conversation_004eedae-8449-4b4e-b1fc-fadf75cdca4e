﻿using Core.Utilities.IoC;
using DataAccess.Abstract;
using DataAccess.Concrete.EntityFramework;
using Entities.Concrete;
using FluentValidation;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Business.ValidationRules.FluentValidation
{
    public class CompanyValidator : AbstractValidator<Company>
    {
        private readonly ICompanyDal _companyDal;

        public CompanyValidator()
        {
            // ServiceTool üzerinden servisleri al
            _companyDal = ServiceTool.ServiceProvider?.GetService<ICompanyDal>();

            RuleFor(c => c.CompanyName).NotEmpty().WithMessage("Şirket adı boş bırakılamaz.");
            RuleFor(c => c.PhoneNumber).NotEmpty().WithMessage("Telefon numarası boş bırakılamaz.");
            RuleFor(p => p.PhoneNumber).Must((company, phone) => BeUniquePhoneNumber(company)).WithMessage("Bu telefon numarası sistemde zaten kayıtlı.");
            RuleFor(p => p.PhoneNumber).Must(StartsWithZero).WithMessage("Telefon numarası 0 ile başlamak zorundadır");
            RuleFor(p => p.PhoneNumber).Length(11).WithMessage("Telefon numarasını kontrol ediniz.");
        }

        private bool BeUniquePhoneNumber(Company company)
        {
            // Null check for DI services - 1000+ salon sisteminde kritik
            if (_companyDal == null)
            {
                // Log the issue for monitoring in production
                System.Diagnostics.Debug.WriteLine("CompanyValidator: _companyDal is null - validation bypassed");
                return true; // Validation geçer, başka katmanda kontrol edilir
            }

            // DI kullanılıyor - Scalability optimized
            if (company.CompanyID != 0)
            {
                // Mevcut şirket güncelleme durumu
                return !_companyDal.GetAll(c =>
                    c.PhoneNumber == company.PhoneNumber &&
                    c.CompanyID != company.CompanyID &&
                    c.IsActive == true).Any();
            }
            // Yeni şirket ekleme durumu
            return !_companyDal.GetAll(c =>
                c.PhoneNumber == company.PhoneNumber &&
                c.IsActive == true).Any();
        }

     

        private bool StartsWithZero(string arg)
        {
            return arg.StartsWith("0");
        }
    }
}