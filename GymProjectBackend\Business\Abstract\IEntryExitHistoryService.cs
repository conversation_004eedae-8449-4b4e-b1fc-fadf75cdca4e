﻿using Core.Utilities.Results;
using Entities.Concrete;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Business.Abstract
{
    public interface IEntryExitHistoryService
    {
        IDataResult<List<EntryExitHistory>> GetAll();
        IResult Add(EntryExitHistory entryExitHistory);
        IResult Update(EntryExitHistory entryExitHistory);
        IResult Delete(int id);
        
        // QR kod taraması için özel metot - CompanyID'yi parametre olarak alır
        IResult AddWithCompanyId(EntryExitHistory entryExitHistory, int companyId);
        
        // QR kod taraması için özel güncelleme metodu - CompanyID'yi parametre olarak alır
        IResult UpdateWithCompanyId(EntryExitHistory entryExitHistory, int companyId);
    }
}
