﻿using Core.Entities.Concrete;
using Core.Utilities.Results;
using Entities.DTOs;

namespace Business.Abstract
{
    public interface IUserOperationClaimService
    {
        IDataResult<List<UserOperationClaimDto>> GetUserOperationClaimDetails();
        IResult Add(UserOperationClaim userOperationClaim);
        IResult Delete(int id);
        IDataResult<List<UserOperationClaim>> GetAll();
        IDataResult<UserOperationClaim> GetById(int id);
        IResult Update(UserOperationClaim userOperationClaim);
        IResult InvalidateUserClaims(int userId); // New method
        IResult AddForRegistration(UserOperationClaim userOperationClaim); // Güvenlik kontrolü olmadan üye kaydı için
    }
}
