using Core.Utilities.Results;
using Core.Utilities.Paging;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Business.Abstract
{
    public interface IExpenseService
    {
        IDataResult<List<Expense>> GetAll(); // Belki DTO daha uygun olabilir, şimdilik Entity
        IDataResult<Expense> GetById(int expenseId);
        IResult Add(Expense expense);
        IResult Update(Expense expense);
        IResult Delete(int expenseId);

        // Filtreleme ve Raporlama için ek metotlar
        IDataResult<List<ExpenseDto>> GetExpensesByDateRange(DateTime startDate, DateTime endDate);

        // Dashboard için optimize edilmiş tek metot - 5 API isteği yerine 1 API isteği
        IDataResult<ExpenseDashboardDto> GetExpenseDashboardData(int year, int month);

        // PaymentHistory benzeri API'ler
        IDataResult<MonthlyExpenseDto> GetMonthlyExpense(int year);
        IDataResult<ExpenseTotals> GetExpenseTotals(ExpensePagingParameters parameters);

        // Pagination ve gelişmiş filtreleme için yeni metotlar
        IDataResult<PaginatedResult<ExpenseDto>> GetExpensesPaginated(ExpensePagingParameters parameters);
        IDataResult<List<ExpenseDto>> GetAllExpensesFiltered(ExpensePagingParameters parameters); // Export için
    }
}