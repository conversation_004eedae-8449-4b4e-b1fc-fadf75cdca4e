﻿using Core.Utilities.Paging;
using Core.Utilities.Results;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Business.Abstract
{
    public interface IPaymentService
    {
        IDataResult<List<Payment>> GetAll();
        IResult Add(Payment payment);
        IResult Update(Payment payment);
        IResult Delete(int id);
        IDataResult<List<PaymentHistoryDto>> GetPaymentHistory();
        IDataResult<List<PaymentHistoryDto>> GetDebtorMembers();
        IResult UpdatePaymentStatus(int paymentId, string paymentMethod);
        IDataResult<PaginatedResult<PaymentHistoryDto>> GetPaymentHistoryPaginated(PaymentPagingParameters parameters);
        IDataResult<PaymentTotals> GetPaymentTotals(PaymentPagingParameters parameters);
        IDataResult<MonthlyRevenueDto> GetMonthlyRevenue(int year);
        IDataResult<List<PaymentHistoryDto>> GetAllPaymentHistoryFiltered(PaymentPagingParameters parameters); // Yeni metod eklendi

    }
}
